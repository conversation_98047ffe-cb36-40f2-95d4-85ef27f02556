import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/all_feed/all_feed_pagination.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_profile/seller_store_profile_screen.dart';
import 'package:swadesic/features/update_email/update_email.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';

enum AllFeedState { Loading, Success, Failed, Empty, NoFeed }

class AllFeedBloc {
  //region Common variable
  late BuildContext context;
  late ScrollController scrollController;
  late AllFeedPagination allFeedPagination;
  List<dynamic> feedList = [];
  bool isPaginationLoading = false;
  AllFeedState currentApiCallStatus = AllFeedState.Success;
  bool hasMoreData = true;

  // Batch loading configuration
  static const int _batchSize = 10;
  static const double _scrollThreshold = 0.8;
  bool _isLoadingMore = false;
  DateTime? _lastLoadTime;

  //endregion

  //region Controller
  final feedStateCtrl = StreamController<AllFeedState>.broadcast();
  //endregion

  //region Constructor
  final bool? isDiscoverable;

  AllFeedBloc(this.context, {this.isDiscoverable}) {
    scrollController = ScrollController();
    allFeedPagination = AllFeedPagination(context, this);

    // Add scroll listener for pagination
    scrollController.addListener(_onScroll);
  }

  void dispose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    feedStateCtrl.close();
    allFeedPagination.dispose();
  }
  //endregion

  //region Scroll listener
  void _onScroll() {
    // Exactly matching the feed implementation
    if (_isLoadingMore || !hasMoreData) {
      return;
    }

    // Check if we've scrolled past threshold - exactly like in feed_bloc.dart
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent * _scrollThreshold) {
      // Throttle loading to prevent rapid successive calls
      final now = DateTime.now();
      if (_lastLoadTime != null &&
          now.difference(_lastLoadTime!).inMilliseconds < 500) {
        return;
      }
      _lastLoadTime = now;

      _loadMoreItems();
    }
  }

  Future<void> _loadMoreItems() async {
    // Exactly matching the feed implementation
    if (_isLoadingMore || !hasMoreData) {
      return;
    }

    _isLoadingMore = true;

    // Call the pagination method - exactly like in feed_bloc.dart
    await getPaginationFeeds();

    _isLoadingMore = false;
  }

  // Implement the getPaginationFeeds method exactly like in feed_bloc.dart
  Future<void> getPaginationFeeds() async {
    try {
      isPaginationLoading = true;

      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      var productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);

      // Use the new lean all feed API for pagination
      final newItems = await PostService().getLeanAllFeeds(
          limit: _batchSize,
          offset: feedList.length,
          context: context,
          isDiscoverable: isDiscoverable);

      if (newItems.isEmpty) {
        hasMoreData = false;
        currentApiCallStatus = AllFeedState.Empty;
        allFeedPagination.feedPaginationStateCtrl.sink
            .add(AllFeedPaginationState.Empty);
        return;
      }

      // Process the new items
      for (var item in newItems) {
        if (item is PostDetail) {
          postDataModel.addPostIntoList(postList: [item]);
        } else if (item is Product) {
          productDataModel.addProductIntoList(products: [item]);
        }
      }

      // Add the new items to the feed list
      feedList.addAll(newItems);

      // Update the UI
      currentApiCallStatus = AllFeedState.Success;
      feedStateCtrl.sink.add(AllFeedState.Success);
    } catch (e) {
      currentApiCallStatus = AllFeedState.Failed;
      feedStateCtrl.sink.add(AllFeedState.Failed);
    } finally {
      isPaginationLoading = false;
    }
  }
  //endregion

  //region Get all feeds - exactly like in feed_bloc.dart
  Future<void> getAllFeeds() async {
    try {
      currentApiCallStatus = AllFeedState.Loading;
      feedStateCtrl.sink.add(AllFeedState.Loading);

      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      var productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);

      // Use the new lean all feed API
      final newFeedList = await PostService().getLeanAllFeeds(
        limit: _batchSize,
        offset: 0,
        context: context,
        isDiscoverable: isDiscoverable,
      );

      // Clear existing lists before processing new items
      feedList.clear();
      postDataModel.allPostDetailList.clear();
      productDataModel.clearAllPosts();

      if (newFeedList.isEmpty) {
        currentApiCallStatus = AllFeedState.Empty;
        feedStateCtrl.sink.add(AllFeedState.Empty);
        return;
      }

      // Process the new items
      for (var item in newFeedList) {
        if (item is PostDetail) {
          postDataModel.addPostIntoList(postList: [item]);
        } else if (item is Product) {
          productDataModel.addProductIntoList(products: [item]);
        }
      }

      // Add the new items to the feed list
      feedList.addAll(newFeedList);

      // Update the UI
      currentApiCallStatus = AllFeedState.Success;
      feedStateCtrl.sink.add(AllFeedState.Success);
    } catch (e) {
      currentApiCallStatus = AllFeedState.Failed;
      feedStateCtrl.sink.add(AllFeedState.Failed);
    }
  }
  //endregion

  //region On tap heart
  Future<void> onTapHeart({required PostDetail postDetail}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      // Update liked count and is liked
      // Toggle the like status
      postDetail.likeStatus = !postDetail.likeStatus!;
      if (postDetail.likeStatus!) {
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      } else {
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      // Refresh UI
      postDataModel.updateUi();
      // API call
      await PostService().likePost(
          postReference: postDetail.postOrCommentReference!,
          likeStatus: postDetail.likeStatus!);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    }
  }
  //endregion

  //region On Tap Share
  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: AppLinkCreateService()
            .createPostLink(postReference: postDetail.postOrCommentReference!),
        imageLink: postDetail.images!.isEmpty
            ? null
            : postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        entityType: EntityType.POST,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.handle,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,
        message: "Checkout this post."
      ),
      context: context,
    );
  }
  //endregion

  //region On tap user or store icon
  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference &&
        AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference &&
        AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to single post view
  void goToSinglePostView({required String postReference}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SinglePostViewScreen(
          postReference: postReference,
        ),
      ),
    );
  }
  //endregion

  //region Init
  Future<void> init() async {
    try {
      // Reset pagination state
      hasMoreData = true;
      _isLoadingMore = false;
      isPaginationLoading = false;

      // Set loading state immediately
      currentApiCallStatus = AllFeedState.Loading;
      feedStateCtrl.sink.add(AllFeedState.Loading);

      // Clear existing data
      feedList.clear();

      // Get fresh data
      await getAllFeeds();

      // Reset scroll position to top
      if (scrollController.hasClients) {
        scrollController.jumpTo(0);
      }
    } catch (e) {
      currentApiCallStatus = AllFeedState.Failed;
      feedStateCtrl.sink.add(AllFeedState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
    }
  }
  //endregion
}
