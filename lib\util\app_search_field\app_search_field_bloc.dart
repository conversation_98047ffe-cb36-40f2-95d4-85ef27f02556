import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/util/common_methods.dart';

class AppSearchFieldBloc {
  // region Common Methods
  BuildContext context;
  bool showSuffix = false;
  // endregion

  //region Controller
  final searchFieldRefreshCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  AppSearchFieldBloc(this.context);

  // endregion

  // region Init
  void init() {
    ///Focus node listen
    // searchFocusNode.addListener(() {
    //   //print(searchFocusNode.hasFocus);
    //   if(!searchFocusNode.hasFocus){
    //     CommonMethods.closeKeyboard(context);
    //   }
    // });
  }
  // endregion

  //region On change
  void onChange({required TextEditingController text}) {
    if (text.text.isNotEmpty) {
      showSuffix = true;
    } else {
      showSuffix = false;
    }
    //print(showSuffix);
    //refresh TextField
    searchFieldRefreshCtrl.sink.add(true);
  }
  //endregion

  //region On tap
  void onTap() {
    // Close keyboard when search field is tapped
    CommonMethods.closeKeyboard(context);
  }
  //endregion

  //region On tap Suffix
  void onTapSuffix({required TextEditingController text}) {
    //Clear text controller
    text.clear();
    //Close keyboard
    CommonMethods.closeKeyboard(context);
    //Hide suffix
    showSuffix = false;
    //refresh TextField
    searchFieldRefreshCtrl.sink.add(true);
  }
  //endregion

//region Dispose
  void dispose() {
    searchFieldRefreshCtrl.close();
  }
//endregion
}
