import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/account_balance/account_balance_transaction/account_balance_order_details/widget/total_amount_to_be_received/total_amount_to_be_received_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/account_balance/account_balance_transaction/account_balance_order_details/widget/total_amount_to_be_received/total_amount_to_be_received_common_widget.dart';
import 'package:swadesic/model/seller_all_order_response/seller_order_details.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_drop_down/app_drop_down.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class TotalAmountToBeReceived extends StatefulWidget {
  final SellerOrdersDetailsResponse sellerOrdersDetailsResponse;
  const TotalAmountToBeReceived({
    Key? key,
    required this.sellerOrdersDetailsResponse,
  }) : super(key: key);

  @override
  State<TotalAmountToBeReceived> createState() =>
      _TotalAmountToBeReceivedState();
}

class _TotalAmountToBeReceivedState extends State<TotalAmountToBeReceived>
    with AutomaticKeepAliveClientMixin<TotalAmountToBeReceived> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;
  // region Bloc
  late TotalAmountToBeReceivedBloc totalAmountToBeReceivedBloc;

  // endregion

  // region Init
  @override
  void initState() {
    //print("visible");
    totalAmountToBeReceivedBloc = TotalAmountToBeReceivedBloc(context);
    super.initState();
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    //print("dispose");
    // TODO: implement dispose
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: totalAmountToBeReceivedBloc.dropDownCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            children: [
              AppDropDown(
                dropDownWidget: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    totalToBeReceive(),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      margin: const EdgeInsets.symmetric(
                          vertical: 15, horizontal: 50),
                      child: divider(),
                    ),
                    amountReceivedTillNow(),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      margin: const EdgeInsets.symmetric(
                          vertical: 15, horizontal: 50),
                      child: divider(),
                    ),
                    notes(),
                  ],
                ),
                collapsedWidget: totalReceivable(),
                dropDownName: AppStrings.amountToBeReceived,
              ),
            ],
          );
        });
  }

  //region Total to be receive
  Widget totalToBeReceive() {
    return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 17.5),
        itemCount:
            widget.sellerOrdersDetailsResponse.totalAmountToBeReceived!.length,
        itemBuilder: (context, index) {
          //if first Show breakup
          if (index == 0) {
            return Container(
              margin: const EdgeInsets.only(bottom: 10),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    widget.sellerOrdersDetailsResponse
                        .totalAmountToBeReceived![index].orderBreakupItemText!,
                    maxLines: 2,
                    style:
                        AppTextStyle.settingText(textColor: AppColors.appBlack),
                  ),
                  horizontalSizedBox(10),
                  InkWell(
                    onTap: () {
                      totalAmountToBeReceivedBloc.showBreakup(
                          priceDetails:
                              widget.sellerOrdersDetailsResponse.grandTotals!);
                    },
                    child: Text(
                      "show breakup",
                      maxLines: 2,
                      style: AppTextStyle.settingText(
                          textColor: AppColors.appBlack, isUnderline: true),
                    ),
                  ),
                  Expanded(child: horizontalSizedBox(50)),
                  Text(
                    "${widget.sellerOrdersDetailsResponse.totalAmountToBeReceived![index].orderBreakupItemValue}",
                    style:
                        AppTextStyle.settingText(textColor: AppColors.appBlack),
                  ),
                ],
              ),
            );
          }
          //if expected amount make it bold just like last one
          if (index == 4) {
            return Column(
              children: [
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      widget
                          .sellerOrdersDetailsResponse
                          .totalAmountToBeReceived![index]
                          .orderBreakupItemText!,
                      maxLines: 2,
                      style: AppTextStyle.settingHeading1(
                          textColor: AppColors.appBlack),
                    ),
                    Expanded(child: horizontalSizedBox(50)),
                    Text(
                      "${widget.sellerOrdersDetailsResponse.totalAmountToBeReceived![index].orderBreakupItemValue}",
                      style: AppTextStyle.settingHeading1(
                          textColor: AppColors.appBlack),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  margin:
                      const EdgeInsets.symmetric(vertical: 15, horizontal: 50),
                  child: divider(),
                ),
              ],
            );
          }
          //if settlement object (last or second-to-last based on disclaimer)
          // Check if current amount disclaimer is present
          bool hasCurrentAmountDisclaimer = widget
              .sellerOrdersDetailsResponse.totalAmountToBeReceived!
              .any((item) =>
                  item.orderBreakupItemSubtext == "CURRENT_AMOUNT_DISCLAIMER");

          // Determine settlement object index
          int settlementIndex = hasCurrentAmountDisclaimer
              ? widget.sellerOrdersDetailsResponse.totalAmountToBeReceived!
                      .length -
                  2
              : widget.sellerOrdersDetailsResponse.totalAmountToBeReceived!
                      .length -
                  1;

          if (index == settlementIndex) {
            return Column(children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    widget.sellerOrdersDetailsResponse
                        .totalAmountToBeReceived![index].orderBreakupItemText!,
                    maxLines: 2,
                    style: AppTextStyle.settingHeading1(
                        textColor: AppColors.appBlack),
                  ),
                  Expanded(child: horizontalSizedBox(50)),
                  Text(
                    "${widget.sellerOrdersDetailsResponse.totalAmountToBeReceived![index].orderBreakupItemValue}",
                    style: AppTextStyle.settingHeading1(
                        textColor: AppColors.appBlack),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              // Container(
              //   padding: const EdgeInsets.symmetric(vertical: 10),
              //   margin: const EdgeInsets.symmetric(vertical: 15,horizontal: 50),
              //   child: divider(),
              // ),
            ]);
          }
          return TotalAmountToBeReceivedCommonWidgets.title(
              priceDetails: widget
                  .sellerOrdersDetailsResponse.totalAmountToBeReceived![index]);
        });
  }
  //endregion

  //region Amount received till now
  Widget amountReceivedTillNow() {
    return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 17.5),
        itemCount:
            widget.sellerOrdersDetailsResponse.amountReceivedTillNow!.length,
        itemBuilder: (context, index) {
          //if First
          if (index == 0) {
            return Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.sellerOrdersDetailsResponse
                      .amountReceivedTillNow![index].orderBreakupItemText!,
                  maxLines: 2,
                  style:
                      AppTextStyle.settingText(textColor: AppColors.appBlack),
                ),
                Expanded(child: horizontalSizedBox(50)),
                Text(
                  "${widget.sellerOrdersDetailsResponse.amountReceivedTillNow![index].orderBreakupItemValue}",
                  style:
                      AppTextStyle.settingText(textColor: AppColors.appBlack),
                ),
              ],
            );
          }
          return TotalAmountToBeReceivedCommonWidgets.title(
              priceDetails: widget
                  .sellerOrdersDetailsResponse.amountReceivedTillNow![index]);
        });
  }
  //endregion

//region Total receivable
  Widget totalReceivable() {
    // Check if current amount disclaimer is present
    bool hasCurrentAmountDisclaimer = widget
        .sellerOrdersDetailsResponse.totalAmountToBeReceived!
        .any((item) =>
            item.orderBreakupItemSubtext == "CURRENT_AMOUNT_DISCLAIMER");

    // Get settlement object based on disclaimer presence
    final settlementObject = hasCurrentAmountDisclaimer
        ? widget.sellerOrdersDetailsResponse.totalAmountToBeReceived![
            widget.sellerOrdersDetailsResponse.totalAmountToBeReceived!.length -
                2] // Second to last if disclaimer present
        : widget.sellerOrdersDetailsResponse.totalAmountToBeReceived![
            widget.sellerOrdersDetailsResponse.totalAmountToBeReceived!.length -
                1]; // Last if no disclaimer

    return Container(
      margin: const EdgeInsets.only(left: 17.5, right: 17.5, bottom: 20),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            settlementObject.orderBreakupItemText!,
            maxLines: 2,
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
          ),
          Expanded(child: horizontalSizedBox(50)),
          Text(
            "${settlementObject.orderBreakupItemValue}",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
          ),
        ],
      ),
    );
  }
//endregion

//region Notes
  Widget notes() {
    return Container(
      margin: const EdgeInsets.only(left: 17.5, right: 17.5, bottom: 20),
      child: Text(
        AppStrings.noteProductsCancelledOrreturn,
        style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
      ),
    );
  }
//endregion
}
