import 'dart:async';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/recommened_posts/recommended_post_pagination.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum RecommendedPostState {
  Loading,
  Success,
  Failed,
  Empty,
  Pagination_Loading,
  Pagination_Empty,
  Pagintion_Failed
}

class RecommendedPostBloc {
  //region Common variable
  late BuildContext context;
  final ScrollController scrollController;
  late RecommendedPostPagination recommendedPostPagination;
  // List<PostDetail> postList = [] ;
  List<dynamic> recommendedPostList = [];

  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final recommendedPostStateCtrl =
      StreamController<RecommendedPostState>.broadcast();

//endregion
  //region Constructor
  RecommendedPostBloc(this.context, this.scrollController);

  //endregion
//region Init
  Future<void> init() async {
    //Initialize Recommended pagination
    recommendedPostPagination = RecommendedPostPagination(context, this);
    //Get all recommended post
    await getAllRecommendedPost();
  }

//endregion

  //region Get recommended post
  Future<void> getAllRecommendedPost({bool isClearOldData = false}) async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    // recommendedPostList = await PostService().getRecommendedPosts(
    //     context: context,
    //     limit: 3, offset: 0);

    // return;
    try {
      //Clear feedList
      recommendedPostList.clear();
      //Api call
      recommendedPostList = await PostService()
          .getRecommendedPosts(context: context, limit: 5, offset: 0);

      //Filter post and product
      for (var postAndProduct in recommendedPostList) {
        if (postAndProduct is PostDetail) {
          //Add data in post data model feed
          postDataModel.addPostIntoList(postList: [postAndProduct]);
          // //print('Post: ${postAndProduct.postOrCommentReference}');
        } else if (postAndProduct is Product) {
          //Add data to product data model
          productDataModel.addProductIntoList(products: [postAndProduct]);
          // //print('Product: ${postAndProduct.productReference}');
        }
      }
      //Empty
      if (recommendedPostList.isEmpty) {
        return recommendedPostStateCtrl.sink.add(RecommendedPostState.Empty);
      }
      //Success
      recommendedPostStateCtrl.sink.add(RecommendedPostState.Success);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Pagination done
      // paginationStateCtrl.sink.add(FeedPaginationState.Pagination_done);
      return recommendedPostStateCtrl.sink.add(RecommendedPostState.Failed);
    } catch (error) {
      //Pagination done
      // paginationStateCtrl.sink.add(FeedPaginationState.Pagination_done);
      //Failed
      return recommendedPostStateCtrl.sink.add(RecommendedPostState.Failed);
    }
  }
//endregion

  //region Get single post
  Future<void> getSinglePost({required String postReference}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      PostDetail postDetail =
          await PostService().getSinglePost(postReference: postReference);
      //Add data in post data model
      postDataModel.addPostIntoList(postList: [postDetail]);
      //If Current post reference is not in the list then return Empty
      if (postDataModel.allPostDetailList
          .where((element) => element.postOrCommentReference == postReference)
          .isEmpty) {
        return recommendedPostStateCtrl.sink.add(RecommendedPostState.Empty);
      }

      //Success
      recommendedPostStateCtrl.sink.add(RecommendedPostState.Success);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      recommendedPostStateCtrl.sink.add(RecommendedPostState.Failed);
      return;
    }
  }
//endregion

  //region On tap user or store icon
  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference &&
        AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference &&
        AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Convert date
  String convertDateFormat({required String inputDateTimeString}) {
    DateTime dateTime = DateTime.parse(inputDateTimeString).toLocal();

    // Formatting the date and time
    String formattedDateTime = DateFormat('h:mma MMM d,yyyy').format(dateTime);

    return formattedDateTime;
  }

  //endregion

  //region On tap image
  void onTapImage({required List<String> imageList, required int index}) {
    Widget screen = BuyerImagePreviewScreen(
      productImage: imageList,
      imageIndex: index,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }

  //endregion

  //region Delete post api call
  Future<void> deletePost({required PostDetail postDetail}) async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //region Try
    try {
      //Api call
      UploadFileService()
          .deletePost(postReference: postDetail.postOrCommentReference!);
      //Remove local data
      //Remove post detail from the userOrStoreFeeds
      // postDataModel.userOrStoreFeedsList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Remove post detail from the allPostDetailList
      postDataModel.allPostDetailList.removeWhere((element) =>
          element.postOrCommentReference == postDetail.postOrCommentReference);
      //Refresh ui
      postDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }

//endregion

  //region On tap heart
  Future<void> onTapHeart({required PostDetail postDetail}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      //Update liked count and is liked
      //Toggle the like status
      postDetail.likeStatus = !postDetail.likeStatus!;
      if (postDetail.likeStatus!) {
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      } else {
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      //Refresh ui
      postDataModel.updateUi();
      //Api call
      await PostService().likePost(
          postReference: postDetail.postOrCommentReference!,
          likeStatus: postDetail.likeStatus!);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      return;
    }
  }
//endregion

  //region Go to edit post
  void goToEditPost({required PostDetail postDetail}) {
    Widget screen = EditPostScreen(
      postDetail: postDetail,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion

  //region On tap drawer
  void onTapDrawer({required PostDetail postDetail}) async {
    List<Map<String, dynamic>> accessOptions = [];
    if (postDetail.createdBy!.userOrStoreReference ==
        (AppConstants.appData.isUserView!
            ? AppConstants.appData.userReference!
            : AppConstants.appData.storeReference!)) {
      accessOptions = [
        //Copy
        {
          'title': AppStrings.copyPostLink,
          'onTap': () {
            Navigator.pop(context);
            CommonMethods.copyText(
                context,
                AppLinkCreateService().createPostLink(
                    postReference: postDetail.postOrCommentReference!));
          },
        },
        //Edit
        {
          'title': AppStrings.editPost.toLowerCase(),
          'onTap': () {
            Navigator.pop(context);
            goToEditPost(postDetail: postDetail);
          },
        },
        //Delete post
        {
          'title': AppStrings.deletePost,
          'onTap': () {
            Navigator.pop(context);
            confirmDelete(postDetail: postDetail);
          },
        },
        // Add more options if needed
      ];
    } else {
      accessOptions = [
        {
          'title': AppStrings.reportThePost,
          'onTap': () {
            Navigator.pop(context);
            // Navigator.pop(context);
            var screen = ReportScreen(
              reference: postDetail.postOrCommentReference!,
              isPostComment: true,
            );
            var route = MaterialPageRoute(builder: (context) => screen);
            Navigator.push(context, route);

//endregion
          },
        },
      ];
    }

    CommonMethods.accessBottomSheet(
      screen: ShareAccessBottomSheet(accessOptions: accessOptions),
      context: context,
    );
  }
  //endregion

  //region Confirm delete
  Future confirmDelete({required PostDetail postDetail}) {
    return CommonMethods.appDialogBox(
        context: context,
        widget: OkayAndCancelDialogScreen(
          onTapSecondButton: () {
            deletePost(postDetail: postDetail);
          },
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.areYouSureWantsToDelete,
          firstButtonName: "Cancel",
          secondButtonName: "Delete",
        ));
  }
//endregion

  //region On Tap Share
  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: AppLinkCreateService()
            .createPostLink(postReference: postDetail.postOrCommentReference!),
        imageLink: postDetail.images!.isEmpty
            ? null
            : postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        entityType: EntityType.POST,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.handle,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,
        message: "Check out this post.",
      ),
      context: context,
    );
  }
  //endregion

  //region Go to single post view
  void goToSinglePostView({required String postReference}) {
    var screen = SinglePostViewScreen(
      postReference: postReference,
      isRepostDetailVisible: true,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

//region Dispose
  void dispose() {
    recommendedPostStateCtrl.close();
    recommendedPostPagination.feedPaginationStateCtrl.close();
  }
//endregion
}
