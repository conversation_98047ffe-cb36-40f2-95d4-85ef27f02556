import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_screen.dart';
import 'package:swadesic/features/buyers/recently_visited_store/recently_visited_store_screen.dart';
import 'package:swadesic/features/buyers/supported_stores/supported_stores_screen.dart';
import 'package:swadesic/features/buyers/user_reward_and_invitees/user_reward_and_invitees_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/follower_and_supportes/follower_and_supporters.dart';
import 'package:swadesic/features/buyers/messaging/new_messaging_chat_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/features/seller/membership/membership_screen.dart';
import 'package:swadesic/features/seller/seller_accounts/seller_accounts_screen.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding_get_started/seller_onboarding_get_started_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/features/support/support_screen.dart';
import 'package:swadesic/features/user_profile/edit_user_profile/edit_user_profile_screen.dart';
import 'package:swadesic/features/user_profile/user_settings/user_settings.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/cloud_messaging/cloud_messaging.dart';
import 'package:swadesic/services/find_your_customers_services/find_your_customers_services.dart';
import 'package:swadesic/services/store_follow_services/store_follow_services.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum UserProfileState { Loading, Success, Failed, Empty }

class UserProfileBloc {
  // region Common Methods
  BuildContext context;
  final String userReference;
  late UserDetailsServices userDetailsServices;

  //Other user detail response
  late GetUserDetailsResponse otherUserInfoResponse;
  late GetUserDetailsResponse loggedInUserInfoResponse;

  ///Tab ctrl
  final TabController tabController;

  ///Logged in user info data model
  late LoggedInUserInfoDataModel loggedInUserInfoDataModel;
  //User detail response
  // late GetUserDetailsResponse getUserDetailsResponse;

  // endregion

  //region Controller
  final userProfileCtrl = StreamController<UserProfileState>.broadcast();
  final followUnFollowStateCtrl = StreamController<AppState>.broadcast();
  final appBarRefresh = StreamController<bool>.broadcast();
  final refreshTabCtrl = StreamController<bool>.broadcast();
  final followCountCtrl = StreamController<bool>.broadcast();

  late ScrollController scrollController = ScrollController();
  //endregion

  // region | Constructor |
  UserProfileBloc(this.context, this.userReference, this.tabController);

  // endregion

  // region Init
  Future<void> init() async {
    loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    userDetailsServices = UserDetailsServices();
    //If User is viewing own profile from buyer side
    if ((AppConstants.appData.isUserView ?? false) &&
        AppConstants.appData.userReference == userReference) {
      //Cloud messaging initialize
      CloudMessaging().initMessaging();
      await getLoggedInUserDetail();
    } else {
      await getUserDetail();
    }
    //Tab controller listener
    tabController.addListener(() {
      refreshTabCtrl.sink.add(true);
    });
    //Scroll listener
    //  scrollController.addListener(() {
    //    if(scrollController.offset >=
    //        scrollController.position.maxScrollExtent &&
    //        !scrollController.position.outOfRange){
    //      //print("Over Scrolled");
    //    }
    //  });
  }

  // endregion

  //region Get Logged in user detail
  Future<void> getLoggedInUserDetail() async {
    try {
      // userProfileCtrl.sink.add(UserProfileState.Loading);
      loggedInUserInfoResponse =
          await userDetailsServices.getLoggedInUserDetail(
              userReference: AppConstants.appData.userReference!);
      //Add user info to logged in user data model
      loggedInUserInfoDataModel.setUserInfoResponse(
          data: loggedInUserInfoResponse.userDetail!);
      //Refresh bottom navigation
      AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
      userProfileCtrl.sink.add(UserProfileState.Success);
    } on ApiErrorResponseMessage catch (error) {
      userProfileCtrl.sink.add(UserProfileState.Failed);
      // context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    } catch (error) {
      userProfileCtrl.sink.add(UserProfileState.Failed);
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
      return;
    }
  }
  //endregion

  //region Get User detail
  Future<void> getUserDetail() async {
    try {
      //Get follow status
      // await checkFollowState();

      //Get user info
      // otherUserInfoResponse = await userDetailsServices.getUserDetail(userResponse: userReference);
      //
      // //Check store follow
      // otherUserInfoResponse.followStatus = await StoreFollowServices().getFollowStatus(reference: otherUserInfoResponse.userDetail!.userReference!);

      // Define the two futures to await
      final userDetailFuture =
          userDetailsServices.getUserDetail(userResponse: userReference);
      final followStatusFuture =
          StoreFollowServices().getFollowStatus(reference: userReference);

      // Wait for both futures to complete
      final List<dynamic> results =
          await Future.wait([userDetailFuture, followStatusFuture]);

      // Extract the results
      otherUserInfoResponse = results[0];
      otherUserInfoResponse.followStatus = results[1];
      //Success
      userProfileCtrl.sink.add(UserProfileState.Success);
    } on ApiErrorResponseMessage catch (error) {
      userProfileCtrl.sink.add(UserProfileState.Failed);
      // context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    } catch (error) {
      userProfileCtrl.sink.add(UserProfileState.Failed);
      return;
    }
  }

  //endregion

  //region Check follow state
  // Future<void> checkFollowState()async{
  //   try {
  //     //Loading
  //     // followUnFollowStateCtrl.sink.add(AppState.Loading);
  //     //Check follow status
  //     otherUserInfoResponse.followStatus = await StoreFollowServices().getFollowStatus(reference: otherUserInfoResponse.userDetail!.userReference!);
  //     //Success
  //     // followUnFollowStateCtrl.sink.add(AppState.Success);
  //
  //   } on ApiErrorResponseMessage {
  //     followUnFollowStateCtrl.sink.add(AppState.Failed);
  //     //CommonMethods.toastMessage(AppStrings.error, context);
  //     return;
  //   } catch (error) {
  //     followUnFollowStateCtrl.sink.add(AppState.Failed);
  //     //CommonMethods.toastMessage(AppStrings.error, context);
  //     return;
  //   }
  // }
  //endregion

  ///Single follow and support
  //region On tap Follow and support
  void onTapFollowAndSupport({required String reference}) async {
    try {
      otherUserInfoResponse.followStatus = await FindYourCustomersServices()
          .followAndUnFollow(reference: reference);
      //If status is Following the increase by 1 else reduce by 1
      if (otherUserInfoResponse.followStatus!.toLowerCase() ==
          FollowEnum.FOLLOWING.name.toLowerCase()) {
        otherUserInfoResponse.followersCount =
            otherUserInfoResponse.followersCount! + 1;
      } else {
        otherUserInfoResponse.followersCount =
            otherUserInfoResponse.followersCount! > 0
                ? otherUserInfoResponse.followersCount! - 1
                : 0;
      }
      followCountCtrl.sink.add(true);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
  //endregion

  //region Go to Seller Accounts Screen
  void goToSellerAccountScreen() async {
    // if static user then open login screen
    if (CommonMethods().isStaticUser()) {
      return CommonMethods().goToSignUpFlow();
    }
    var screen = SellerAccountsScreen(userReference: userReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion
  // region Go to user reword and invitees screen
  void goToUserAndInviteesScreen() async {
    // if static user then open login screen
    if (CommonMethods().isStaticUser()) {
      return CommonMethods().goToSignUpFlow();
    }
    var screen = const UserRewardAndInviteesScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Go to Membership Screen
  void goToMembership() {
    var screen = const MembershipScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

//endregion

  //region Go to User settings
  void goToUserSettings() {
    var screen = const UserProfileSettingsScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // getUserDetail();
    });
  }

//endregion

  //region Go to Edit profile screen
  void editProfileScreen() {
    var screen = EditUserProfileScreen(
      userDetail: ((AppConstants.appData.isUserView ?? false) &&
              AppConstants.appData.userReference == userReference)
          ? loggedInUserInfoResponse.userDetail!
          : otherUserInfoResponse.userDetail!,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      getLoggedInUserDetail();
      // getUserDetail();
    });
  }

//endregion

//region Go to create new store
  void goToCreateNewStore() {
    ///Check access
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.createStore != "1") {
      CommonMethods.toastMessage(AppStrings.noAccess, context);
      return;
    }
    var screen = SellerOnBoardingGetStartedScreen(
      isTestStore: true,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }

//endregion

//region Go to My order screen
  void goToMyOrderScreen() {
    var screen = const BuyerMyOrderScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region On Tap Favourite store
  onTapFavouriteStore() {
    var screen = const SupportedStoresScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region On Tap Find your friends
  goToFindYourCustomer() async {
    //If web view
    if (kIsWeb) {
      return await CommonMethods.appDownloadDialog();
    }
    //Else if static user then open login screen
    else if (CommonMethods().isStaticUser()) {
      return CommonMethods().goToSignUpFlow();
    }
    var screen = FindYourCustomersScreen(
      visibleNext: false,
      title: AppStrings.findYourFriendsOnSwadesic,
    );
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region On tap report
  void onTapReport() {
    //On tap open a dialog
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => const AlertDialog(
        content: IssueSuggestionDialog(),
      ),
    );
  }
  //endregion

  //region On tap share user profile
  onTapShareUserProfile({required String userName}) async {
    //Create universal link
    String url = "${AppConstants.domainName}$userName";

    String inviteCode = "";
    // If non-static user
    if (!CommonMethods().isStaticUser()) {
      //If User view
      if (AppConstants.appData.isUserView ?? false) {
        LoggedInUserInfoDataModel userDetailDataModel =
            Provider.of<LoggedInUserInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = userDetailDataModel.userDetail!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          url = "$url/?ic=$inviteCode";
        }
      }
      //Store view
      else {
        //Seller admin data model
        SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
            Provider.of<SellerOwnStoreInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = sellerOwnStoreInfoDataModel.storeInfo!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          url = "$url/?ic=$inviteCode";
        }
      }
    }

    // Get user details
    UserDetail userDetail;
    String? userIcon;
    String? displayName;
    String? handle;

    // Determine if we're sharing the logged-in user or another user
    if ((AppConstants.appData.isUserView ?? false) &&
        AppConstants.appData.userReference == userReference) {
      userDetail = loggedInUserInfoResponse.userDetail!;
    } else {
      userDetail = otherUserInfoResponse.userDetail!;
    }

    userIcon = userDetail.icon;
    displayName = userDetail.displayName ?? userDetail.firstName;
    handle = userName; // Use the username as the handle

    // Use ShareWithImageScreen instead of direct share
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: url,
        imageLink: userIcon,
        imageType: CustomImageContainerType.user,
        entityType: EntityType.USER,
        postCreatorName: handle,
        postCreatorIcon: userIcon,
        postText: displayName, // Use handle as the text to display
        objectReference: userReference,
        message: "Check out $displayName's profile on Swadesic!",
      ),
      context: context,
    );
  }
  //endregion

  //region On Tap recently visited store
  onTapRecentlyVisitedStore() {
    //RecentlyVisitedStoreScreen

    var screen = const RecentlyVisitedStoreScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region On Tap Support
  onTapSupport() {
    AppConstants.userPersistentTabController.jumpToTab(4);
    AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  }
//endregion

  //region if from bottom navigation switch to home
  goToFirstBottomNavigation() {
    AppConstants.userPersistentTabController.jumpToTab(0);
    AppConstants.userPersistentTabController.jumpToTab(0);
    AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  }

  //endregion
  //region On Tap Go to support
  onTapGoToSupport({required bool isReport}) {
    var screen = SupportScreen(
      isReport: isReport,
      // No target store reference since this is general support from user profile
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Check is it already in follow follower sy or not

  //endregion

  //region Go to report screen
  void goToReportScreen() async {
    await Future.delayed(Duration.zero);
    var screen = ReportScreen(
      reference: otherUserInfoResponse.userDetail!.userReference!,
      isUser: true,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to follower and supporters
  void goToFollowerAndSupporters() {
    var screen = FollowerAndSupporters(
      reference: userReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion
  // region Go to login screen
  void goToLoginScreen() {
    var screen = const MobileNumberOtpScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion

  // region Go to Message
  void goToMessage() {
    // Get the appropriate user info response based on whether viewing own profile or other user's profile
    final userInfoResponse = ((AppConstants.appData.isUserView ?? false) &&
            AppConstants.appData.userReference == userReference)
        ? loggedInUserInfoResponse
        : otherUserInfoResponse;

    final userDetail = userInfoResponse.userDetail;

    // Navigate to new messaging chat screen
    NewMessagingChatScreen.navigateToChat(
      context,
      connectingId: userDetail?.newMessagingUserId ?? '',
      chatName: userDetail?.displayName ??
          userDetail?.firstName ??
          userDetail?.userName ??
          'User',
      chatIcon: userDetail?.icon ?? '',
      entityType: 'USER',
      chatOwnerReference: userDetail?.userReference ?? '',
    );
  }
  //endregion

//region Dispose
  void dispose() {
    userProfileCtrl.close();
    appBarRefresh.close();
    scrollController.dispose();
  }
//endregion
}
