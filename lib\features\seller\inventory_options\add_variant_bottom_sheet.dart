import 'package:flutter/material.dart';
import 'package:swadesic/model/product_option/product_option.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/common_methods.dart';

class AddVariantBottomSheet extends StatefulWidget {
  final List<ProductOption> productOptions;
  final List<ProductVariant> existingVariants;
  final ProductVariant? existingVariant;
  final Function(ProductVariant) onVariantAdded;
  final VoidCallback? onVariantDeleted;

  const AddVariantBottomSheet({
    super.key,
    required this.productOptions,
    required this.existingVariants,
    this.existingVariant,
    required this.onVariantAdded,
    this.onVariantDeleted,
  });

  @override
  State<AddVariantBottomSheet> createState() => _AddVariantBottomSheetState();
}

class _AddVariantBottomSheetState extends State<AddVariantBottomSheet> {
  late TextEditingController stockController;
  late TextEditingController mrpController;
  late TextEditingController sellingPriceController;

  Map<String, String> selectedCombinations = {};
  bool isEditing = false;

  @override
  void initState() {
    super.initState();
    isEditing = widget.existingVariant != null;

    stockController = TextEditingController(
      text: widget.existingVariant?.stock.toString() ?? '',
    );
    mrpController = TextEditingController(
      text: widget.existingVariant?.mrpPrice.toString() ?? '',
    );
    sellingPriceController = TextEditingController(
      text: widget.existingVariant?.sellingPrice.toString() ?? '',
    );

    // Add listeners for real-time updates
    mrpController.addListener(() {
      setState(() {});
    });
    sellingPriceController.addListener(() {
      setState(() {});
    });

    if (widget.existingVariant != null) {
      selectedCombinations =
          Map<String, String>.from(widget.existingVariant!.combinations);
    }
  }

  @override
  void dispose() {
    stockController.dispose();
    mrpController.dispose();
    sellingPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Center(
                child: Text(
                  isEditing
                      ? AppStrings.editVariant
                      : AppStrings.createAVariant,
                  style:
                      AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
                ),
              ),
              verticalSizedBox(30),

              // Select product options
              Text(
                AppStrings.selectProductOptions,
                style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
              ),
              verticalSizedBox(20),

              // Option selections
              ...widget.productOptions
                  .map((option) => buildOptionSelection(option)),

              verticalSizedBox(30),

              // Stock input
              Text(
                AppStrings.inStock,
                style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
              ),
              verticalSizedBox(10),
              AppTextFields.onlyNumberTextField(
                context: context,
                textEditingController: stockController,
                hintText: AppStrings.inStockQuantity,
              ),
              verticalSizedBox(20),

              // MRP and Selling price
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppStrings.mrp,
                          style: AppTextStyle.heading2Bold(
                              textColor: AppColors.appBlack),
                        ),
                        verticalSizedBox(10),
                        AppTextFields.onlyNumberTextField(
                          context: context,
                          textEditingController: mrpController,
                          hintText: AppStrings.mrp,
                        ),
                      ],
                    ),
                  ),
                  horizontalSizedBox(15),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppStrings.sellingPrice,
                          style: AppTextStyle.heading2Bold(
                              textColor: AppColors.appBlack),
                        ),
                        verticalSizedBox(10),
                        AppTextFields.onlyNumberTextField(
                          context: context,
                          textEditingController: sellingPriceController,
                          hintText: AppStrings.sellingPrice,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              verticalSizedBox(20),

              // Price validation disclaimer
              if (mrpController.text.isNotEmpty &&
                  sellingPriceController.text.isNotEmpty) ...[
                buildPriceValidationMessage(),
                verticalSizedBox(20),
              ],

              // Action buttons
              if (isEditing) ...[
                // Update and Delete buttons for editing
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          // Check if this is the last variant
                          if (widget.existingVariants.length <= 1) {
                            // Show toast message if trying to delete the last variant
                            CommonMethods.toastMessage(
                                "At least one variant should be present",
                                context);
                            return;
                          }

                          if (widget.onVariantDeleted != null) {
                            widget.onVariantDeleted!();
                            Navigator.pop(context);
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.red),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              AppStrings.delete,
                              style: AppTextStyle.contentText0(
                                  textColor: Colors.red),
                            ),
                          ),
                        ),
                      ),
                    ),
                    horizontalSizedBox(15),
                    Expanded(
                      flex: 2,
                      child: GestureDetector(
                        onTap: updateVariant,
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          decoration: BoxDecoration(
                            color: AppColors.appBlack,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              AppStrings.updateVariant,
                              style: AppTextStyle.contentText0(
                                  textColor: AppColors.appWhite),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ] else ...[
                // Add button for new variant
                GestureDetector(
                  onTap: addVariant,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                      color: AppColors.appBlack,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        AppStrings.addVariant,
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appWhite),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget buildOptionSelection(ProductOption option) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          option.optionName,
          style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: option.optionValues.map((value) {
            final isSelected = selectedCombinations[option.optionName] == value;
            return GestureDetector(
              onTap: () {
                setState(() {
                  selectedCombinations[option.optionName] = value;
                });
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.appBlack : AppColors.appWhite,
                  border: Border.all(
                    color: isSelected
                        ? AppColors.appBlack
                        : AppColors.textFieldFill1,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  value,
                  style: AppTextStyle.contentText0(
                    textColor:
                        isSelected ? AppColors.appWhite : AppColors.appBlack,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        verticalSizedBox(20),
      ],
    );
  }

  Widget buildPriceCalculation() {
    final mrp = int.tryParse(mrpController.text) ?? 0;
    final sellingPrice = int.tryParse(sellingPriceController.text) ?? 0;

    if (mrp > 0 && sellingPrice > 0) {
      final discount = ((mrp - sellingPrice) / mrp * 100).round();
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${AppStrings.oneUnitOfThisProductSellsAt}$sellingPrice",
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          ),
          if (discount > 0) ...[
            verticalSizedBox(5),
            Text(
              "${AppStrings.discount}$discount%",
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ],
        ],
      );
    }
    return const SizedBox.shrink();
  }

  Widget buildPriceValidationMessage() {
    final mrp = int.tryParse(mrpController.text) ?? 0;
    final sellingPrice = int.tryParse(sellingPriceController.text) ?? 0;

    if (mrp > 0 && sellingPrice > 0) {
      if (sellingPrice > mrp) {
        // Show red disclaimer when selling price is greater than MRP
        return Text(
          "Selling price should not be greater than MRP",
          style: AppTextStyle.contentText0(textColor: Colors.red),
        );
      } else {
        // Show price calculation when logic is correct
        final discount = ((mrp - sellingPrice) / mrp * 100).round();
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${AppStrings.oneUnitOfThisProductSellsAt}$sellingPrice",
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
            if (discount > 0) ...[
              verticalSizedBox(5),
              Text(
                "${AppStrings.discount}$discount%",
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
            ],
          ],
        );
      }
    }
    return const SizedBox.shrink();
  }

  void addVariant() {
    if (!validateInputs()) return;

    final variant = ProductVariant(
      combinations: selectedCombinations,
      mrpPrice: int.parse(mrpController.text),
      sellingPrice: int.parse(sellingPriceController.text),
      stock: int.parse(stockController.text),
    );

    widget.onVariantAdded(variant);
    Navigator.pop(context);
  }

  void updateVariant() {
    if (!validateInputs()) return;

    final variant = widget.existingVariant!.copyWith(
      combinations: selectedCombinations,
      mrpPrice: int.parse(mrpController.text),
      sellingPrice: int.parse(sellingPriceController.text),
      stock: int.parse(stockController.text),
    );

    widget.onVariantAdded(variant);
    Navigator.pop(context);
  }

  bool validateInputs() {
    // Check if all options are selected
    for (var option in widget.productOptions) {
      if (!selectedCombinations.containsKey(option.optionName)) {
        CommonMethods.toastMessage(
            "${AppStrings.pleaseSelectOption} ${option.optionName}", context);
        return false;
      }
    }

    // Validate numeric inputs
    if (stockController.text.isEmpty ||
        int.tryParse(stockController.text) == null) {
      CommonMethods.toastMessage(
          AppStrings.pleaseEnterValidStockQuantity, context);
      return false;
    }

    final mrp = int.tryParse(mrpController.text);
    if (mrpController.text.isEmpty || mrp == null || mrp <= 0) {
      CommonMethods.toastMessage(AppStrings.pleaseEnterValidMRP, context);
      return false;
    }

    final sellingPrice = int.tryParse(sellingPriceController.text);
    if (sellingPriceController.text.isEmpty ||
        sellingPrice == null ||
        sellingPrice <= 0) {
      CommonMethods.toastMessage(
          AppStrings.pleaseEnterValidSellingPrice, context);
      return false;
    }

    // Validate that selling price is not greater than MRP
    if (sellingPrice > mrp) {
      CommonMethods.toastMessage(
          "Selling price should not be greater than MRP", context);
      return false;
    }

    return true;
  }
}
