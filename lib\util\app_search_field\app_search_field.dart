import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field_bloc.dart';
import 'package:swadesic/util/app_text_style.dart';

//region App Search field
class AppSearchField extends StatefulWidget {
  final TextEditingController textEditingController;
  final dynamic onChangeText;
  final bool isActive;
  final dynamic onTapSuffix;
  final String hintText;
  final dynamic onSubmit;
  final bool isAutoFocus;
  final FocusNode? focusNode;
  final VoidCallback? onTap; // Added onTap callback
  const AppSearchField({
    Key? key,
    required this.textEditingController,
    this.onChangeText,
    this.isActive = true,
    this.onTapSuffix,
    required this.hintText,
    this.onSubmit,
    this.isAutoFocus = false,
    this.focusNode,
    this.onTap,
  }) : super(key: key);

  @override
  State<AppSearchField> createState() => _AppSearchFieldState();
}
//endregion

class _AppSearchFieldState extends State<AppSearchField> {
  //region Bloc
  late AppSearchFieldBloc appSearchFieldBloc;
  //endregion

  //region Init
  @override
  void initState() {
    appSearchFieldBloc = AppSearchFieldBloc(context);
    appSearchFieldBloc.init();

    super.initState();
  }

  //endregion
  //region Dispose
  @override
  void dispose() {
    appSearchFieldBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: appSearchFieldBloc.searchFieldRefreshCtrl.stream,
        builder: (context, snapshot) {
          return SizedBox(
            height: 43,
            child: TextField(
              //Focus node
              focusNode: widget.focusNode,
              autofocus: widget.isAutoFocus,
              // focusNode: appSearchFieldBloc.searchFocusNode,
              controller: widget.textEditingController,
              // autofillHints: [AutofillHints.email,],
              textCapitalization: TextCapitalization.sentences,

              ///On tap outside
              // onTapOutside: (value){
              //   CommonMethods.closeKeyboard(context);
              // },
              ///On change field
              onChanged: (value) {
                widget.onChangeText(value);
                appSearchFieldBloc.onChange(text: widget.textEditingController);
              },

              ///On tap
              onTap: () {
                // !widget.isActive?widget.textEditingController.clear():null;
                // Call the onTap callback if provided
                if (widget.onTap != null) {
                  widget.onTap!();
                }
                // Call the bloc's onTap method
                appSearchFieldBloc.onTap();
              },

              ///On submit
              onSubmitted: (value) {
                if (widget.onSubmit == null) {
                  return;
                }
                widget.onSubmit();
              },
              enabled: widget.isActive,

              ///Style
              style:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack0),
              // style: const TextStyle(fontFamily:AppConstants.rRegular,
              //     fontSize: 14, fontWeight: FontWeight.w400,
              //     height: 1.19,
              //     color: AppColors.appBlack),

              decoration: InputDecoration(
                contentPadding: const EdgeInsets.only(bottom: 10, top: 10),
                filled: true,
                fillColor: AppColors.textFieldFill1,

                hintText: widget.hintText,

                ///Hint text style
                hintStyle:
                    AppTextStyle.hintText(textColor: AppColors.writingBlack1),
                // .copyWith(
                //   overflow: TextOverflow.ellipsis,
                // ),
                ///Suffix icon
                // suffix: Icon(Icons.close),

                suffixIcon: Visibility(
                  // visible: appSearchFieldBloc.showSuffix,
                  visible: widget.textEditingController.text.isNotEmpty,
                  child: InkWell(
                    // padding: EdgeInsets.zero,
                    onTap: () {
                      appSearchFieldBloc.onTapSuffix(
                          text: widget.textEditingController);
                      widget.onTapSuffix == null ? "" : widget.onTapSuffix();
                    },
                    child: Container(
                        padding: const EdgeInsets.all(10),
                        // padding: const EdgeInsets.symmetric(vertical: 15,horizontal: 15),
                        child: SvgPicture.asset(
                          AppImages.close,
                          color: AppColors.darkGray,
                        )),
                  ),
                ),

                ///Prefix icon
                prefixIcon: Container(
                    padding: const EdgeInsets.all(10),
                    child: SvgPicture.asset(AppImages.searchBarIcon)),

                ///Focus border
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(22),
                  borderSide:
                      BorderSide(color: AppColors.textFieldFill1, width: 1.5),
                ),

                ///Disable border
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(22),
                  borderSide:
                      BorderSide(color: AppColors.textFieldFill1, width: 1.5),
                ),

                ///Enable border
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(22),
                  borderSide:
                      BorderSide(color: AppColors.textFieldFill1, width: 1.5),
                ),
              ),
            ),
          );
        });
  }
//endregion
}
