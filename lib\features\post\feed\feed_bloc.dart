import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/feed/feed_pagination.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/update_email/update_email.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_list_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/seller_home_service/seller_home_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum FeedState { Loading, Success, Failed, Empty, NoFeed }

class FeedBloc {
  //region Common variable
  late BuildContext context;
  final String userOrStoreFeedReference;
  late ScrollController scrollController;
  late FeedPagination feedPagination;
  List<dynamic> feedList = [];
  bool isPaginationLoading = false;
  FeedState currentApiCallStatus = FeedState.Success;
  bool hasMoreData = true;

  // Store creation announcement variables
  bool showStoreCreationAnnouncement = false;
  bool isCheckingStoreCreation = true;

  // Batch loading configuration
  static const int _batchSize = 10;
  static const double _scrollThreshold = 0.8;
  bool _isLoadingMore = false;
  DateTime? _lastLoadTime;

  //endregion

  //region Controller
  final feedStateCtrl = StreamController<FeedState>.broadcast();
  final appUpdateCheckCtrl = StreamController<bool>.broadcast();
  final updateEmailCtrl = StreamController<bool>.broadcast();
  final storeCreationAnnouncementCtrl = StreamController<bool>.broadcast();
  //endregion

  //region Constructor
  FeedBloc(this.context, this.userOrStoreFeedReference,
      {ScrollController? scrollController}) {
    this.scrollController = scrollController ?? ScrollController();
    _initScrollController();
    feedPagination = FeedPagination(context, this);
  }
  //endregion

  void _initScrollController() {
    if (!scrollController.hasClients) {
      scrollController.addListener(_optimizedScrollListener);
    }
  }

  void _optimizedScrollListener() {
    if (_isLoadingMore || !hasMoreData) return;

    // Check if we've scrolled past threshold
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent * _scrollThreshold) {
      // Throttle loading to prevent rapid successive calls
      final now = DateTime.now();
      if (_lastLoadTime != null &&
          now.difference(_lastLoadTime!).inMilliseconds < 500) {
        return;
      }
      _lastLoadTime = now;

      _loadMoreData();
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !hasMoreData) return;

    _isLoadingMore = true;
    await getPaginationFeeds();
    _isLoadingMore = false;
  }

  Future<void> _processBatch(List<dynamic> items, PostDataModel postDataModel,
      ProductDataModel productDataModel) async {
    final posts = <PostDetail>[];
    final products = <Product>[];

    for (final item in items) {
      try {
        if (item is PostDetail) {
          posts.add(item);
        } else if (item is Product) {
          products.add(item);
        }
      } catch (e) {
        // Log error but continue processing other items
        debugPrint('Error processing feed item: $e');
      }
    }

    if (posts.isNotEmpty) {
      postDataModel.addPostIntoList(postList: posts);
    }
    if (products.isNotEmpty) {
      productDataModel.addProductIntoList(products: products);
    }
  }

  Future<void> getPaginationFeeds() async {
    if (isPaginationLoading || !hasMoreData) return;

    try {
      isPaginationLoading = true;

      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      var productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);

      // Use the new lean feed API for pagination
      final newItems = await PostService().getLeanFeeds(
          limit: _batchSize, offset: feedList.length, context: context);
      if (newItems.isEmpty) {
        hasMoreData = false;
        currentApiCallStatus = FeedState.Empty;
        feedPagination.feedPaginationStateCtrl.sink
            .add(FeedPaginationState.Empty);
        return;
      }

      // Process all items in a single batch
      await _processBatch(newItems, postDataModel, productDataModel);

      // Update feed list after processing
      feedList.addAll(newItems);
    } catch (error) {
      debugPrint('Error in getPaginationFeeds: $error');
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
    } finally {
      isPaginationLoading = false;
    }
  }

  Future<void> getAllFeeds() async {
    try {
      currentApiCallStatus = FeedState.Loading;
      feedStateCtrl.sink.add(FeedState.Loading);

      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      var productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);

      // Use the new lean feed API
      final newFeedList = await PostService()
          .getLeanFeeds(limit: _batchSize, offset: 0, context: context);

      // Clear existing lists before processing new items
      feedList.clear();
      postDataModel.allPostDetailList.clear();
      productDataModel.clearAllPosts();

      if (newFeedList.isEmpty) {
        currentApiCallStatus = FeedState.Empty;
        feedStateCtrl.sink.add(FeedState.Empty);
        return;
      }

      // Process all items in a single batch to ensure type safety
      await _processBatch(newFeedList, postDataModel, productDataModel);

      // Update feed list after processing
      feedList = List.from(newFeedList);
      hasMoreData = newFeedList.length >= _batchSize;

      currentApiCallStatus = FeedState.Success;
      feedStateCtrl.sink.add(FeedState.Success);
    } catch (error) {
      debugPrint('Error in getAllFeeds: $error');
      currentApiCallStatus = FeedState.Failed;
      feedStateCtrl.sink.add(FeedState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
    }
  }
  //endregion

  //region Navigation methods
  void goToSinglePostView({required PostDetail postDetail}) {
    var route = MaterialPageRoute(
      builder: (context) => SinglePostViewScreen(
        postReference: postDetail.postOrCommentReference!,
        isRepostDetailVisible: true,
      ),
    );
    Navigator.push(context, route);
  }

  void goToEditPost({required PostDetail postDetail}) {
    var route = MaterialPageRoute(
      builder: (context) => EditPostScreen(
        postDetail: postDetail,
      ),
    );
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }

  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference &&
        AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference &&
        AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  void confirmDelete({required PostDetail postDetail}) {
    CommonMethods.appDialogBox(
        context: context,
        widget: OkayAndCancelDialogScreen(
          onTapSecondButton: () {
            deletePost(postDetail: postDetail);
          },
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.areYouSureWantsToDelete,
          firstButtonName: "Cancel",
          secondButtonName: "Delete",
        ));
  }

  void onTapDrawer({required PostDetail postDetail}) {
    List<Map<String, dynamic>> accessOptions = [];
    if (postDetail.createdBy!.userOrStoreReference ==
        (AppConstants.appData.isUserView!
            ? AppConstants.appData.userReference!
            : AppConstants.appData.storeReference!)) {
      accessOptions = [
        //Copy
        {
          'title': AppStrings.copyPostLink,
          'onTap': () {
            Navigator.pop(context);
            CommonMethods.copyText(
                context,
                AppLinkCreateService().createPostLink(
                    postReference: postDetail.postOrCommentReference!));
          },
        },
        //Edit
        {
          'title': AppStrings.editPost.toLowerCase(),
          'onTap': () {
            Navigator.pop(context);
            goToEditPost(postDetail: postDetail);
          },
        },
        //Delete post
        {
          'title': AppStrings.deletePost,
          'onTap': () {
            Navigator.pop(context);
            confirmDelete(postDetail: postDetail);
          },
        },
        // Add more options if needed
      ];
    } else {
      accessOptions = [
        {
          'title': AppStrings.reportThePost,
          'onTap': () {
            Navigator.pop(context);
            var screen = ReportScreen(
              reference: postDetail.postOrCommentReference!,
              isPostComment: true,
            );
            var route = MaterialPageRoute(builder: (context) => screen);
            Navigator.push(context, route);
          },
        },
      ];
    }

    CommonMethods.accessBottomSheet(
      screen: ShareAccessBottomSheet(accessOptions: accessOptions),
      context: context,
    );
  }

  void onTapHeart({required PostDetail postDetail}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      //Update liked count and is liked
      //Toggle the like status
      postDetail.likeStatus = !postDetail.likeStatus!;
      if (postDetail.likeStatus!) {
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      } else {
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      //Refresh ui
      postDataModel.updateUi();
      //Api call
      await PostService().likePost(
          postReference: postDetail.postOrCommentReference!,
          likeStatus: postDetail.likeStatus!);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      return;
    }
  }

  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: AppLinkCreateService()
            .createPostLink(postReference: postDetail.postOrCommentReference!),
        imageLink: postDetail.images!.isEmpty
            ? null
            : postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        entityType: EntityType.POST,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.handle,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,
        message: "Check out this post.",
      ),
      context: context,
    );
  }

  void checkUpdate() {
    // Implement update check
  }

  Future<void> deletePost({required PostDetail postDetail}) async {
    try {
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      // API call
      await UploadFileService()
          .deletePost(postReference: postDetail.postOrCommentReference!);

      // Remove post detail from the allPostDetailList
      postDataModel.allPostDetailList.removeWhere((element) =>
          element.postOrCommentReference == postDetail.postOrCommentReference);

      // Remove from feed list
      feedList.removeWhere((element) =>
          element is PostDetail &&
          element.postOrCommentReference == postDetail.postOrCommentReference);

      // Refresh UI
      postDataModel.updateUi();
    } on ApiErrorResponseMessage catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
    }
  }

  void goToEmailVerify() {
    var screen = const UpdateEmail();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  void onTapReport() {
    // Show report dialog or navigate to report screen
    CommonMethods.toastMessage("Report functionality coming soon", context);
  }
  //endregion

  //region Check Store Creation Announcement
  Future<void> checkStoreCreationAnnouncement() async {
    try {
      // Get the logged in user info
      final loggedInUserInfoDataModel =
          Provider.of<LoggedInUserInfoDataModel>(context, listen: false);

      // Check if user is logged in
      if (loggedInUserInfoDataModel.userDetail != null) {
        // Check if user has SELLER role
        if (loggedInUserInfoDataModel.userDetail!.userRoles!
                .contains("SELLER") ==
            true) {
          // Check if user has any stores
          final sellerHomeService = SellerHomeService();
          final storeListResponse = await sellerHomeService.getSellerStore();

          // Show announcement if user has no stores
          if (storeListResponse.storeList == null ||
              storeListResponse.storeList!.isEmpty) {
            showStoreCreationAnnouncement = true;
          } else {
            showStoreCreationAnnouncement = false;
          }
        } else {
          showStoreCreationAnnouncement = false;
        }
      } else {
        showStoreCreationAnnouncement = false;
      }
    } catch (e) {
      debugPrint("Error checking store creation announcement: $e");
      showStoreCreationAnnouncement = false;
    } finally {
      isCheckingStoreCreation = false;
      storeCreationAnnouncementCtrl.sink.add(showStoreCreationAnnouncement);
    }
  }
  //endregion

  //region Init
  Future<void> init() async {
    try {
      // Reset pagination state
      hasMoreData = true;
      _isLoadingMore = false;
      isPaginationLoading = false;

      // Set loading state immediately
      currentApiCallStatus = FeedState.Loading;
      feedStateCtrl.sink.add(FeedState.Loading);

      // Clear existing data
      feedList.clear();

      // Get fresh data
      await getAllFeeds();

      // Check if we should show store creation announcement
      await checkStoreCreationAnnouncement();

      // Reset scroll position to top
      if (scrollController.hasClients) {
        scrollController.jumpTo(0);
      }
    } catch (e) {
      currentApiCallStatus = FeedState.Failed;
      feedStateCtrl.sink.add(FeedState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
    }
  }
  //endregion

  void dispose() {
    // Only dispose the scroll controller if we created it
    if (!scrollController.hasListeners) {
      scrollController.dispose();
    }

    // Close all stream controllers
    feedStateCtrl.close();
    appUpdateCheckCtrl.close();
    updateEmailCtrl.close();
    storeCreationAnnouncementCtrl.close();
  }
}
