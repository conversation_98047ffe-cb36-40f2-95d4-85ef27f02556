import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

// import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:marquee/marquee.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/variant_display_widget/variant_display_widget.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/seller/membership/membership_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_scrollable_text/app_scrollable_text.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_field_style.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/card_expire_format.dart';
import 'package:swadesic/util/card_number_format.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/container_decorations/container_decoration.dart';
import 'package:swadesic/util/date_formater.dart';
import 'app_colors.dart';
import 'package:badges/badges.dart' as badges;
import 'package:badges/badges.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';
import 'package:swadesic/widgets/themed_image_icon.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';

///Buyers
//region Buyers Common Widgets

//region Buyer App Bar
AppBar buyerAppBar(
    {required Widget leadingIcon,
    required BuildContext context,
    String titleText = '',
    bool drawerIconEnable = false,
    onTapDrawer,
    bool leadingVisible = true,
    bool messageVisible = true,
    bool basketVisible = true,
    bool searchVisible = false,
    bool dropdownButton = false,
    onTapLeading,
    onPressDropdown,
    onTapBasket,
    onTapMessage}) {
  // Retrieve the data from the StoreInfoModel
  ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
      Provider.of<ShoppingCartQuantityDataModel>(context);

  return AppBar(
    backgroundColor: AppColors.appWhite,
    leading: leadingVisible
        ? CupertinoButton(
            onPressed: () {
              if (onTapLeading == null) {
                Navigator.pop(context);
              } else {
                onTapLeading();
              }
              // onTapLeading();
            },
            padding: EdgeInsets.zero,
            child: leadingIcon)
        : const SizedBox(),

    titleSpacing: 0,

    centerTitle: false,
    title: Text(
      titleText,
      style: TextStyle(
        fontFamily: "LatoBold",
        fontSize: 19,
        fontWeight: FontWeight.w700,
        color: AppColors.appBlack,
      ),
    ),
    elevation: 0,
    automaticallyImplyLeading: false,
    //region Next Button

    actions: [
      CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          var screen = const MembershipScreen();
          var route = MaterialPageRoute(builder: (context) => screen);
          Navigator.push(context, route);
        },
        child: SvgPicture.asset(
          AppImages.membership,
          color: AppColors.appBlack,
          fit: BoxFit.cover,
        ),
      ),
      // Visibility(
      //   visible: messageVisible,
      //   child: InkWell(
      //       onTap: () {
      //         onTapMessage();
      //         //addProductBloc.goBackToSellerHomeScreen();
      //       },
      //       //padding: EdgeInsets.zero,
      //       child: SvgPicture.asset(
      //         AppImages.messageIcon,
      //         fit: BoxFit.contain,
      //         color: AppColors.appBlack,
      //       )),
      // ),
      // horizontalSizedBox(10),
      ///Basket
      Visibility(
        visible: basketVisible,
        child: shoppingCartQuantityDataModel.productReferenceList == 0
            ? Container(
                margin: const EdgeInsets.only(top: 10, bottom: 10, left: 10),
                child: SvgPicture.asset(
                  AppImages.basket,
                  fit: BoxFit.contain,
                  color: AppColors.appBlack,
                ),
              )
            : InkWell(
                onTap: () {
                  onTapBasket();
                },
                child: Container(
                  margin: const EdgeInsets.only(top: 10, bottom: 10, left: 10),
                  child: Align(
                    child: badges.Badge(
                      badgeStyle: BadgeStyle(
                        padding: EdgeInsets.all(5),
                        badgeColor: AppColors.yellow,
                      ),
                      badgeContent: Text(
                        shoppingCartQuantityDataModel.productReferenceList
                            .toString(),
                        style: TextStyle(
                            color: AppColors.appWhite,
                            fontSize: 10,
                            fontWeight: FontWeight.w700),
                      ),
                      //alignment: Alignment.center,
                      position: BadgePosition.topEnd(),
                      child: SvgPicture.asset(
                        AppImages.basket,
                        fit: BoxFit.contain,
                        color: AppColors.appBlack,
                      ),
                    ),
                  ),
                ),
              ),
      ),

      ///Search icon
      Visibility(
        visible: searchVisible,
        child: InkWell(
          onTap: () {},
          child: SvgPicture.asset(
            AppImages.searchBarIcon,
            fit: BoxFit.contain,
            color: AppColors.appBlack,
          ),
        ),
      ),

      ///Dropdown
      Visibility(
          visible: dropdownButton,
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              onPressDropdown();
            },
            child: SvgPicture.asset(
              AppImages.downArrow2,
              color: AppColors.appBlack,
              fit: BoxFit.cover,
            ),
          )),

      ///Drawer
      drawerIconEnable
          ? InkWell(
              // padding: EdgeInsets.zero,
              onTap: () {
                onTapDrawer();
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                margin:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                child: SvgPicture.asset(AppImages.drawerIcon),
              ),
            )
          : horizontalSizedBox(10)
    ],

    //endregion
  );
}
//endregion
//endregion

///Sellers

///Common Text Feld Widgets

//region  Border Text Field
Widget borderTextField(
  TextEditingController textFieldCtrl,
  String textFieldHint,
  int textFieldMaxLine,
  TextInputType keyBoardType, {
  dynamic onChange,
  Color? borderColor,
  TextAlign textAlign = TextAlign.start,
  maxLength = 20000,
  dynamic onEditingComplete,
}) {
  borderColor ??= AppColors.darkStroke;
  return TextFormField(
    onChanged: (value) {
      if (onChange == null) {
        return;
      }
      onChange();
    },
    keyboardType: keyBoardType,
    maxLines: textFieldMaxLine,
    controller: textFieldCtrl,
    //maxLength: maxLength,
    style: AppTextStyle.heading4SemiBold(textColor: AppColors.appBlack),
    textAlign: textAlign,
    onEditingComplete: () {
      onEditingComplete();
    },
    textCapitalization: TextCapitalization.sentences,
    inputFormatters: [
      LengthLimitingTextInputFormatter(maxLength),
    ],
    // scrollPadding: EdgeInsets.only(bottom:MediaQuery.of(AppConstants.userStoreCommonBottomNavigationContext).viewInsets.bottom + 40.0),
    decoration: InputDecoration(
      filled: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      fillColor: AppColors.appWhite,
      isDense: true,
      hintText: textFieldHint,
      hintStyle:
          AppTextStyle.heading2Regular(textColor: AppColors.writingColor3),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: borderColor, width: 1.5),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: borderColor, width: 1.5),
      ),
    ),
  );
}
//endregion

//region  Color Filled
Widget colorFilledTextField({
  required BuildContext context,
  required TextEditingController textFieldCtrl,
  required String hintText,
  required int textFieldMaxLine,
  required TextInputType keyboardType,
  required TextInputAction textInputAction,
  int minLines = 1,
  onChangeText,
  onTapTextField,
  onEditingComplete,
  String regExp = "[a-zA-z ]",
  int maxCharacter = 24,
  Color? fillColor,
  Color? borderColor,
  Color? hintTextColor,
  TextAlign textAlign = TextAlign.start,
  double hintFontSize = 14,
  double styleFontSize = 14,
  double hintTextLetterSpacing = 0.0,
  double letterSpacing = 0.0,
  bool isObscureText = false,
  bool isCardNumberFormat = false,
  bool isCardExpireFormat = false,
  bool isDateMonthYearFormat = false,
  bool isRefCodeFormat = false,
  bool allowSpace = true,
  TextCapitalization fieldTextCapitalization = TextCapitalization.sentences,
  bool enableTextField = true,
  double contentPaddingVertical = 10,
  double contentPaddingHorizontal = 10,
  onSaved,
  Color? textColor,
  FontWeight textFontWeight = FontWeight.w400,
  String hintFontFamily = AppConstants.rRegular,
}) {
  borderColor ??= AppColors.textFieldFill1;
  hintTextColor ??= AppColors.writingColor3;
  textColor ??= AppColors.appBlack;
  fillColor ??= AppColors.appWhite;
  var refFormat = MaskTextInputFormatter(
    mask: 'A - AAAA - AAAA',
    filter: {"A": RegExp(r'[^>]')},
  );
  return TextFormField(
    // onEditingComplete: (){
    //   onEditingComplete();
    // },

    obscureText: isObscureText,
    textAlign: textAlign,
    enabled: enableTextField,
    textCapitalization: fieldTextCapitalization,
    inputFormatters: <TextInputFormatter>[
      FilteringTextInputFormatter.allow(RegExp(regExp)),
      isRefCodeFormat
          ? refFormat
          : LengthLimitingTextInputFormatter(maxCharacter),
      isDateMonthYearFormat
          ? DateFormatter()
          : LengthLimitingTextInputFormatter(maxCharacter),
      isCardNumberFormat
          ? CardNumberFormatter()
          : LengthLimitingTextInputFormatter(maxCharacter),
      isCardExpireFormat
          ? CardExpirationFormatter()
          : LengthLimitingTextInputFormatter(maxCharacter),
      LengthLimitingTextInputFormatter(maxCharacter)
    ],
    scrollPadding:
        EdgeInsets.only(bottom: MediaQuery.of(context).size.height / 5),

    onEditingComplete: () {
      CommonMethods.closeKeyboard(context);
      onEditingComplete == null ? Container() : onEditingComplete();
    },
    onTap: () {
      onTapTextField == null ? Container() : onTapTextField();
    },
    onChanged: (value) {
      onChangeText == null ? Container() : onChangeText();
    },
    onSaved: (value) {
      onSaved();
    },
    onTapOutside: (value) {
      //print("outside");
    },

    textInputAction: textInputAction,
    keyboardType: keyboardType,
    controller: textFieldCtrl,
    maxLines: textFieldMaxLine,
    minLines: minLines,

    style: AppTextStyle.heading3Regular(textColor: textColor),

    //controller: textFieldCtrl,
    // style: TextStyle(
    //     fontFamily: AppConstants.rRegular, letterSpacing: letterSpacing, fontSize: styleFontSize, fontWeight: textFontWeight, color: textColor),
    decoration: InputDecoration(
        isDense: true,
        hintStyle:
            AppTextStyle.heading3Regular(textColor: AppColors.writingBlack),
        fillColor: AppColors.textFieldFill1,
        // Specify the desired internal color
        filled: true,
        hintText: hintText,
        contentPadding:
            const EdgeInsets.symmetric(vertical: 11.5, horizontal: 16.0),
        border: AppTextFieldStyle.filledColored(
            fieldColors: AppColors.textFieldFill1),
        focusedBorder: AppTextFieldStyle.filledColored(
            fieldColors: AppColors.textFieldFill1),
        enabledBorder: AppTextFieldStyle.filledColored(
            fieldColors: AppColors.textFieldFill1),
        disabledBorder: AppTextFieldStyle.filledColored(
            fieldColors: AppColors.textFieldFill1),
        focusedErrorBorder: AppTextFieldStyle.filledColored(
            fieldColors: AppColors.textFieldFill1),
        errorBorder: AppTextFieldStyle.filledColored(
            fieldColors: AppColors.textFieldFill1)),
  );
}
//endregion

//region Under Line TextField
Widget underLineTextField(
    {required BuildContext context,
    required TextEditingController textFieldCtrl,
    required String textFieldHint,
    required int textFieldMaxLine,
    required TextInputType keyboardType,
    required TextInputAction textInputAction,
    onChangeText,
    onTapTextField,
    // onEditingComplete,
    isEnable = true}) {
  return TextFormField(
    textCapitalization: TextCapitalization.sentences,
    // onEditingComplete: (){
    //   onEditingComplete();
    // },
    // onEditingComplete: () {
    //   CommonMethods.closeKeyboard(context);
    //   onEditingComplete == null ? Container() : onEditingComplete();
    // },
    enabled: isEnable,
    onSaved: (value) {},
    onTap: () {
      onTapTextField == null ? Container() : onTapTextField();
    },
    onChanged: (value) {
      onChangeText == null ? Container() : onChangeText();
    },
    minLines: 1,

    textInputAction: textInputAction,
    keyboardType: keyboardType,
    controller: textFieldCtrl,
    maxLines: textFieldMaxLine,

    //controller: textFieldCtrl,
    style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack),
    decoration: InputDecoration(
      //filled: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
      //fillColor: AppColors.lightWhite,
      isDense: true,
      hintText: textFieldHint,
      // hintStyle: const TextStyle(
      //   fontSize: 16,
      //   fontWeight: FontWeight.w400,
      // ),
      hintStyle:
          AppTextStyle.heading2Regular(textColor: AppColors.writingColor3),

      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: AppColors.appBlack, width: 1),
      ),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: AppColors.appBlack, width: 1),
      ),
    ),
  );
}
//endregion

//region Sellers Common Widgets
//region Seller App Bar
AppBar sellerAppBar(
    {required Widget leadingIcon,
    onTapLeading,
    String titleText = '',
    onTapDrawerButton,
    String actionButtonText = '',
    Color? actionButtonTextColor,
    onTapActionButton,
    required BuildContext screenContext,
    bool drawerVisible = true}) {
  actionButtonTextColor ??= AppColors.brandBlack;
  return AppBar(
    backgroundColor: AppColors.appWhite,
    leading: CupertinoButton(
        onPressed: () {
          onTapLeading == null ? Navigator.pop(screenContext) : onTapLeading();
          //onTapLeading();
        },
        padding: EdgeInsets.zero,
        child: leadingIcon),
    titleSpacing: 0,

    centerTitle: false,
    title: Text(
      titleText,
      style: TextStyle(
        fontFamily: "LatoBold",
        fontSize: 19,
        fontWeight: FontWeight.w700,
        color: AppColors.appBlack,
      ),
    ),
    elevation: 0,
    automaticallyImplyLeading: false,
    //region Next Button

    actions: [
      CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          onTapActionButton();
        },
        child: Center(
          child: Text(
            actionButtonText,
            style: TextStyle(
                fontWeight: FontWeight.w700,
                fontFamily: "LatoBold",
                fontSize: 19,
                color: actionButtonTextColor),
          ),
        ),
      ),
      CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          var screen = const MembershipScreen();
          var route = MaterialPageRoute(builder: (context) => screen);
          Navigator.push(screenContext, route);
        },
        child: SvgPicture.asset(
          AppImages.membership,
          color: AppColors.appBlack,
          fit: BoxFit.cover,
        ),
      ),
      drawerVisible
          ? CupertinoButton(
              // padding: EdgeInsets.zero,
              onPressed: () {
                onTapDrawerButton();
              },
              child: SvgPicture.asset(
                AppImages.drawerIcon,
                color: AppColors.appBlack,
                fit: BoxFit.fill,
              ),
            )
          : const SizedBox(
              width: 30,
            ),
    ],

    //endregion
  );
}
//endregion

//endregion

///Common For Buyers and Sellers
///

//region Messaging App bar
AppBar messagingAppBar({
  required Widget leadingIcon,
  String titleText = '',
  onTapDrawerButton,
  String actionButtonText = '',
  onTapActionButton,
  required BuildContext screenContext,
}) {
  return AppBar(
    //toolbarHeight: 100,
    backgroundColor: AppColors.appWhite,
    leading: CupertinoButton(
        onPressed: () {
          Navigator.pop(screenContext);
          //onTapLeading();
        },
        padding: EdgeInsets.zero,
        child: leadingIcon),
    titleSpacing: 0,
    centerTitle: false,
    title: SizedBox(
      height: 35,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 35,
            color: Colors.green,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                appText("nykaa_online",
                    fontFamily: AppConstants.rRegular,
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                    color: AppColors.appBlack),
                appText("active 1 hour ago",
                    fontFamily: AppConstants.rRegular,
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                    color: AppColors.appBlack),
              ],
            ),
          )
        ],
      ),
    ),
    elevation: 0,
    automaticallyImplyLeading: false,
    //region Next Button
    actions: [
      //Exclamation
      CupertinoButton(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        onPressed: () {},
        child: SvgPicture.asset(
          AppImages.exclamation,
          color: AppColors.appBlack,
          fit: BoxFit.contain,
        ),
      ),
      //Shield
      CupertinoButton(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        onPressed: () {},
        child: SvgPicture.asset(
          AppImages.shield,
          color: AppColors.appBlack,
          fit: BoxFit.contain,
        ),
      ),
      //Drawer
      CupertinoButton(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        onPressed: () {},
        child: SvgPicture.asset(
          AppImages.drawerIcon,
          color: AppColors.appBlack,
          fit: BoxFit.contain,
        ),
      )
    ],

    //endregion
  );
}
//endregion

//region App text
Widget appText(String text,
    {int maxLine = 1,
    double fontSize = 16,
    Color? color,
    fontFamily = AppConstants.rRegular,
    FontWeight fontWeight = FontWeight.w600,
    TextAlign textAlign = TextAlign.start,
    FontStyle style = FontStyle.normal,
    double opacity = 1,
    double height = 1,
    bool lineThrough = false,
    bool isUnderLine = false}) {
  color ??= AppColors.appBlack;
  return Text(
    text,
    maxLines: maxLine,
    overflow: TextOverflow.visible,
    textAlign: textAlign,
    style: TextStyle(
        height: height,
        letterSpacing: 0.24,
        fontFamily: fontFamily,
        color: color.withOpacity(opacity),
        fontSize: fontSize,
        fontWeight: fontWeight,
        overflow: TextOverflow.visible,
        fontStyle: style,
        decoration: isUnderLine
            ? TextDecoration.underline
            : lineThrough
                ? TextDecoration.lineThrough
                : TextDecoration.none),
  );
}
//endregion

//region App text 2
Widget appText2(
    {String text = "",
    int maxLine = 1,
    double fontSize = 16,
    Color? color,
    fontFamily = AppConstants.rRegular,
    FontWeight fontWeight = FontWeight.w600,
    TextAlign textAlign = TextAlign.start,
    FontStyle style = FontStyle.normal,
    double opacity = 1,
    bool isUnderLine = false}) {
  color ??= AppColors.appBlack;
  return Text(
    text,
    maxLines: maxLine,
    overflow: TextOverflow.visible,
    textAlign: textAlign,
    style: TextStyle(
        fontFamily: fontFamily,
        color: color.withOpacity(opacity),
        fontSize: fontSize,
        fontWeight: fontWeight,
        overflow: TextOverflow.visible,
        fontStyle: style,
        decoration:
            isUnderLine ? TextDecoration.underline : TextDecoration.none),
  );
}
//endregion

//region App Copy
Widget appCopy(BuildContext context, String text) {
  return Padding(
    padding: const EdgeInsets.only(left: 10),
    child: CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        CommonMethods.copyText(context, text);
      },
      child: Container(
        decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
            borderRadius: BorderRadius.all(Radius.circular(20))),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
        child: Text(
          "copy",
          style: TextStyle(
              fontFamily: "LatoSemibold",
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: AppColors.writingColor3),
        ),
      ),
    ),
  );
}
//endregion

//region Brand green

//region Common For Buyers and Sellers
//region Vertical Sized box
Widget verticalSizedBox(double height) {
  return SizedBox(
    height: height,
  );
}
//endregion

//region Horizontal Sized box
Widget horizontalSizedBox(double width) {
  return SizedBox(
    width: width,
  );
}
//endregion

//region Divider
Widget divider() {
  return Divider(
    color: AppColors.borderColor1,
    height: 1,
    thickness: 1,
  );
}
//endregion

//region Tip
Widget commonTooltip(String message) {
  return Container(
    color: AppColors.appWhite,
    child: Align(
      alignment: Alignment.centerLeft,
      child: Container(
        decoration: BoxDecoration(
            //color: Colors.red,
            border: Border.all(width: 1.0, color: AppColors.lightStroke),
            borderRadius: const BorderRadius.all(Radius.circular(30))),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              AppImages.yellowLightBulb,
              fit: BoxFit.cover,
            ),
            horizontalSizedBox(5),
            Expanded(
              child: Text(
                message,
                maxLines: 1,
                style: TextStyle(
                    fontSize: 12,
                    overflow: TextOverflow.visible,
                    fontFamily: "LatoRegular",
                    fontWeight: FontWeight.w400,
                    color: AppColors.appBlack),
              ),
            ),
            horizontalSizedBox(5),
            SvgPicture.asset(
              AppImages.close,
              fit: BoxFit.cover,
            ),

            // Expanded(child: horizontalSizedBox(5)),
          ],
        ),
      ),
    ),
  );
}
//endregion

//region Text Field heading and text field
Widget textFieldAndHeading(
    {required Color fieldNameColor,
    required String fieldName,
    required BuildContext context,
    required bool isIconVisible,
    required String fieldHint,
    required maxFieldLine,
    required TextEditingController textEditingController}) {
  return Column(
    children: [
      ///Additional notes
      Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          appText(fieldName,
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: AppColors.appBlack,
              maxLine: maxFieldLine,
              fontFamily: AppConstants.rRegular),
          verticalSizedBox(10),
          isIconVisible
              ? SvgPicture.asset(AppImages.exclamation)
              : verticalSizedBox(10),
        ],
      ),
      verticalSizedBox(10),
      colorFilledTextField(
        context: context,
        textFieldCtrl: textEditingController,
        hintText: fieldHint,
        textFieldMaxLine: 3,
        keyboardType: TextInputType.name,
        textInputAction: TextInputAction.done,
        // onChangeText: sellerOnBoardingBloc.onTextChange,
        regExp: AppConstants.acceptAll,
        fieldTextCapitalization: TextCapitalization.words,
        maxCharacter: 50,
      ),
    ],
  );
}
//endregion

//region Buyer and seller order Bottom sheet

//endregion

//region Shopping Cart Add New Address TextField
// Widget colorFilledTextField(TextEditingController textFieldCtrl,String textFieldHint,int textFieldMaxLine,double textFieldWidth,TextInputType keyBoardType,borderColor){
//   return SizedBox(
//     width: textFieldWidth,
//     child: TextFormField(
//       keyboardType: keyBoardType,
//       controller: textFieldCtrl,
//       maxLines: textFieldMaxLine,
//       //controller: textFieldCtrl,
//       style: TextStyle(
//           fontFamily: "LatoRegular",
//           fontSize: 14,
//           fontWeight: FontWeight400,
//           color: AppColors.appBlack),
//       decoration: InputDecoration(
//
//         filled: true,
//
//
//
//         // contentPadding: EdgeInsets.symmetric(vertical: 10,horizontal: 10),
//
//
//         fillColor: AppColors.lightWhite,
//
//
//         isDense: true,
//
//         hintText: textFieldHint,
//         hintStyle: TextStyle(
//           fontSize: 12,
//           fontWeight: FontWeight400,
//         ),
//         focusedBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(8.r),
//           borderSide: BorderSide(
//               color: AppColors.lightWhite,
//               width: 1.5
//
//           ),
//
//         ),
//         enabledBorder: OutlineInputBorder(
//
//           borderRadius: BorderRadius.circular(8.r),
//           borderSide: BorderSide(
//               color: AppColors.lightWhite,
//               width: 1.5
//
//           ),
//         ),
//
//
//
//       ),
//
//     ),
//   );
// }
//endregion

//endregion

//region No Product Text
Widget commonErrorMessage(String messsge) {
  return Text(
    messsge,
    textAlign: TextAlign.center,
    style: TextStyle(
      fontWeight: FontWeight.w700,
      color: AppColors.writingColor2,
      fontSize: 16,
      fontFamily: "LatoBold",
    ),
  );
}
//endregion

//region Error Text
Widget errorText() {
  return Text(
    AppStrings.commonErrorMessage,
    textAlign: TextAlign.center,
    style: TextStyle(
      fontWeight: FontWeight.w700,
      color: AppColors.writingColor2,
      fontSize: 16,
      fontFamily: "LatoBold",
    ),
  );
}
//endregion

//region Extended image
Widget extendedImage(
  String? imageUrl,
  BuildContext context,
  int? cacheHeight,
  int? cacheWidth, {
  String? customPlaceHolder,
  bool isFromImagePreview = false,

  // required bool isStoreImage,
  // required bool isProductImage,
  // required bool isUserImage,
  double borderRadiusValue = 0.0,
  Color borderColor = Colors.transparent,
  BoxShape boxShape = BoxShape.rectangle,
  bool cache = true,
  BoxFit fit = BoxFit.cover,
  double imageHeight = double.infinity,
  double imageWidth = double.infinity,
}) {
  //If url is null
  if (imageUrl == null) {
    //If from image view
    if (isFromImagePreview) {
      return const SizedBox();
    }
    return customPlaceHolder == null
        ? Image.asset(
            AppImages.noImage,
            fit: fit,
            cacheHeight: cacheHeight,
            cacheWidth: cacheWidth,
          )
        : SvgPicture.asset(
            customPlaceHolder,
            fit: BoxFit.fill,
          );
  }

  //If empty url
  if (imageUrl.isEmpty) {
    //If from image view
    if (isFromImagePreview) {
      return const SizedBox();
    }
    return customPlaceHolder == null
        ? Image.asset(
            AppImages.noImage,
            fit: fit,
            cacheHeight: cacheHeight,
            cacheWidth: cacheWidth,
          )
        : SvgPicture.asset(
            customPlaceHolder,
            fit: BoxFit.cover,
          );
  }

  // Format the image URL
  imageUrl = imageUrl.startsWith('/media/')
      ? "${AppConstants.baseUrl}$imageUrl"
      : "${AppConstants.baseUrl}${AppConstants.baseMediaUrlWithslashes}$imageUrl";

  // For all valid image URLs, use CachedNetworkImage with loading indicator
  return CachedNetworkImage(
    imageUrl: imageUrl,
    height: imageHeight,
    width: imageWidth,
    fadeInDuration:
        const Duration(milliseconds: 0), // Disable fade-in animation
    imageBuilder: (context, imageProvider) => Container(
      width: imageHeight.toDouble(),
      height: imageWidth.toDouble(),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: imageProvider,
          fit: fit,
        ),
      ),
    ),
    // Use progressIndicatorBuilder to show loader only until image starts loading
    progressIndicatorBuilder: (context, url, downloadProgress) {
      //If from image view
      if (isFromImagePreview) {
        return const SizedBox();
      }

      // Hide the loader in almost all cases - we want to be very aggressive about not showing it
      // Only show the loader when we're absolutely sure we're at the very beginning of loading
      // This ensures the loader disappears as soon as possible
      if (downloadProgress.progress == null ||
          downloadProgress.progress! > 0 ||
          downloadProgress.downloaded > 0 ||
          downloadProgress.totalSize == null) {
        return const SizedBox();
      }

      // If we've reached this point, we're at 0% progress and know the total size
      // Show the loader for a very brief moment

      // Only show loader during initial loading phase (when progress is 0 or null)
      return Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.brandBlack),
          ),
        ),
      );
    },
    errorWidget: (context, url, error) {
      //If from image view
      if (isFromImagePreview) {
        return const SizedBox();
      }
      // Show placeholder image only when there's an error loading the image
      return customPlaceHolder == null
          ? Image.asset(
              AppImages.noImage,
              fit: BoxFit.fill,
              cacheHeight: cacheHeight,
              cacheWidth: cacheWidth,
            )
          : SvgPicture.asset(
              customPlaceHolder,
              fit: BoxFit.fill,
            );
    },
  );
}
//endregion

//endregion

//region Reset and Apply
Widget resetApply({required onTapReset, required onTapApply}) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 20),
    child: Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        InkWell(
          onTap: () {
            onTapReset();
          },
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Text(
              AppStrings.reset,
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            ),
            // child: appText(
            //   AppStrings.reset,
            //   fontFamily: AppConstants.rRegular,
            //   fontSize: 15,
            //   fontWeight: FontWeight.w600,
            //   color: AppColors.appBlack,
            // ),
          ),
        ),
        Expanded(
          child: InkWell(
            onTap: () {
              onTapApply();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(80),
                  color: AppColors.brandBlack),
              child: Text(
                AppStrings.apply,
                style: AppTextStyle.access0(textColor: AppColors.appWhite),
              ),
              // child: appText(
              //   AppStrings.apply,
              //   fontFamily: AppConstants.rRegular,
              //   fontSize: 15,
              //   fontWeight: FontWeight.w700,
              //   color: AppColors.white,
              // ),
            ),
          ),
        )
      ],
    ),
  );
}
//endregion

//region App radio check box
Widget appRadioCheckBox({
  required bool isActive,
  required String text,
  required bool isExpand,
  required onTap,
  double buttonVerticalPadding = 10,
  bool isRadio = true,
  Color? radioActiveColor,
  double fontSize = 16,
  Color? checkBoxActiveColor,
  Color? textColor,
}) {
  radioActiveColor ??= AppColors.brandBlack;
  checkBoxActiveColor ??= AppColors.brandBlack;
  textColor ??= AppColors.appBlack;
  return Theme(
    ///Un-comment
    // data: ThemeData(unselectedWidgetColor: AppColors.lightWhite2),
    data: ThemeData(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
    ),

    child: InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ///Radio
            isRadio
                ? Container(
                    margin: const EdgeInsets.only(
                        top: 5, bottom: 5, left: 5, right: 10),
                    height: 24,
                    width: 24,
                    child: isActive
                        ? SvgPicture.asset(
                            AppImages.appRadioTrue,
                            fit: BoxFit.fill,
                            color: radioActiveColor,
                          )
                        : SvgPicture.asset(
                            AppImages.appRadioFalse,
                            fit: BoxFit.fill,
                            // color: AppColors.darkGray,
                          ))
                :

                ///Check box
                Container(
                    margin: const EdgeInsets.only(
                        top: 5, bottom: 5, left: 5, right: 10),
                    height: 24,
                    width: 24,
                    child: isActive
                        ? SvgPicture.asset(
                            AppImages.appCheckBoxTrue,
                            fit: BoxFit.fill,
                            color: checkBoxActiveColor,
                          )
                        : SvgPicture.asset(
                            AppImages.appCheckBoxFalse,
                            fit: BoxFit.fill,
                            color: AppColors.darkGray,
                          )),
            isExpand
                ? Expanded(
                    child: Text(
                      text,
                      style: AppTextStyle.contentText0(textColor: textColor),
                    ),
                  )
                : Text(
                    text,
                    style: AppTextStyle.contentText0(textColor: textColor),
                  ),

            // appText(text,
            //         fontFamily: AppConstants.rRegular, color: AppColors.writingColor2, fontSize: fontSize, fontWeight: FontWeight.w400, maxLine: 5)
            //
          ],
        ),
      ),
    ),
  );
}
//endregion

//region Search field
Widget appSearchField(
    {required TextEditingController textCtrl, required String hintText}) {
  return SizedBox(
    height: 40,
    child: TextFormField(
      autofocus: false,
      controller: textCtrl,
      onChanged: (value) {},

      maxLines: 1,
      //controller: addProductBloc.hashTagsTextCtrl,
      // readOnly: true,

      style: TextStyle(
          fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack),
      decoration: InputDecoration(
        prefixIcon: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: SvgPicture.asset(
            AppImages.searchBarIcon,
            fit: BoxFit.contain,
            color: AppColors.appBlack7,
          ),
        ),
        filled: true,
        contentPadding: const EdgeInsets.symmetric(vertical: 10),
        fillColor: AppColors.textFieldFill1,
        isDense: true,
        hintText: hintText,
        hintStyle: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: AppColors.appBlack.withOpacity(0.4)),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(22),
            borderSide: BorderSide.none),
        enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(22),
            borderSide: BorderSide.none),
      ),
    ),
  );
}
//endregion

//region App Check box
// Widget appCheckBox({required bool isActive,required String text,required bool isExpand,required onTap}){
//   return InkWell(
//     onTap: (){
//       onTap();
//     },
//     child: Container(
//       alignment: Alignment.center,
//       padding: const EdgeInsets.symmetric(horizontal: 5,vertical:10 ),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           SizedBox(
//               height: 20,
//               width: 20,
//               child: SvgPicture.asset(isActive?AppImages.appCheckBoxTrue:AppImages.appCheckBoxFalse,fit: BoxFit.fill)),
//           horizontalSizedBox(15),
//           isExpand?Expanded(child: appText(text,fontFamily: AppConstants.rRegular,color: AppColors.writingColor2,fontSize: 16,fontWeight: FontWeight.w400,maxLine: 5)):
//           appText(text,fontFamily: AppConstants.rRegular,color: AppColors.writingColor2,fontSize: 16,fontWeight: FontWeight.w400,maxLine: 5)
//         ],
//       ),
//     ),
//   );
// }
//endregion

class AppCommonWidgets with AppContainerDecoration {
  ///1. Button gradient
  //region Button gradient
  static Widget buttonGradient() {
    return Container(
      decoration: const BoxDecoration(),
    );
  }

  //endregion

  ///Appbar
  //region Main app bar
  static AppBar mainAppBar({
    //Context
    required BuildContext context,
    Color? backgroundColor,
    Color? leadingIconColor,

    //Leading
    onTapLeading,
    bool isLeadingVisible = true,
    // String leadingIcon = AppImages.backButton,
    String leadingIcon = AppImages.backButton,
    bool isCustomLeadingIcon = false,

    //Title
    bool isTitleVisible = true,
    bool isCustomTitle = false,
    String title = "",
    Widget customTitleWidget = const SizedBox(),
    bool isCenterTitle = false,
    //Membership
    bool isMembershipVisible = true,
    //Cart
    bool isCartVisible = true,
    //Text button
    bool isTextButtonVisible = false,
    Widget textButtonWidget = const SizedBox(),
    dynamic onTapTextButton,
    //Default Menu button
    bool isDefaultMenuVisible = true,
    dynamic onTapReport,
    onTapDrawer,
    //Custom Menu button
    bool isCustomMenuVisible = false,
    Widget customMenuButton = const SizedBox(),
    //Drop down
    bool isDropdownVisible = false,
    bool isRotateDropdownIcon = false,
    onTapDropdown,
    //Filter
    bool isFilterVisible = false,
    dynamic onTapFilter,
    dynamic onTapCart,

    // height
    double? toolbarHeight = 48,
  }) {
    backgroundColor ??= AppColors.appWhite;
    leadingIconColor ??= AppColors.appBlack;
    // Retrieve the data from the StoreInfoModel
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context);

    return AppBar(
      backgroundColor: backgroundColor,
      titleSpacing: 4, // Reduced from default 16
      elevation: 0,
      automaticallyImplyLeading: false,
      centerTitle: isCenterTitle,
      primary: true,
      toolbarHeight: toolbarHeight, // Reduced from default 56

      ///Leading
      leading: isLeadingVisible
          ? InkWell(
              onTap: () {
                if (onTapLeading == null) {
                  Navigator.pop(context);
                } else {
                  onTapLeading();
                }
                // onTapLeading();
              },
              // padding: EdgeInsets.only(left: 0),
              // child: SvgPicture.asset(leadingIcon,height: 25,width: 25,),
              child: isCustomLeadingIcon
                  ? Padding(
                      padding: const EdgeInsets.only(
                          top: 10, bottom: 5.0, left: 14.0, right: 14.0),
                      child: SvgPicture.asset(
                        leadingIcon,
                        height: 25,
                        width: 25,
                        color: AppColors.appBlack,
                      ),
                    )
                  : Icon(
                      Icons.arrow_back_rounded,
                      size: 24, // Reduced from 30
                      color: leadingIconColor,
                    ),
            )
          : null,
      // leading: Visibility(
      //   visible: isLeadingVisible,
      //   child: InkWell(
      //     onTap: () {
      //       if (onTapLeading == null) {
      //         Navigator.pop(context);
      //       } else {
      //         onTapLeading();
      //       }
      //       // onTapLeading();
      //     },
      //     // padding: EdgeInsets.only(left: 0),
      //     // child: SvgPicture.asset(leadingIcon,height: 25,width: 25,),
      //     child: isCustomLeadingIcon
      //         ? Padding(
      //             padding: const EdgeInsets.all(10.0),
      //             child: SvgPicture.asset(
      //               leadingIcon,
      //               height: 25,
      //               width: 25,
      //             ),
      //           )
      //         :  Icon(
      //             Icons.arrow_back_rounded,
      //             size: 30,
      //             color: leadingIconColor,
      //           ),
      //   ),
      // ),

      ///Title text
      title: isCustomTitle
          ? customTitleWidget
          : Padding(
              padding: EdgeInsets.only(left: isLeadingVisible ? 00 : 20),
              child: AppCommonWidgets.appBarTitleText(text: title),
            ),
      // title: Text(
      //   titleText,
      //   style: const TextStyle(
      //     fontFamily: AppConstants.rRegular,
      //     fontSize: 19,
      //     fontWeight: FontWeight.w700,
      //     color: AppColors.appBlack,
      //   ),
      // ),

      ///Custom title

      ///region Button
      actions: [
        ///Membership
        Visibility(
          ///Un comment this
          // visible: isMembershipVisible,
          visible: false,
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              var screen = const MembershipScreen();
              var route = MaterialPageRoute(builder: (context) => screen);
              Navigator.push(context, route);
            },
            child: SvgPicture.asset(
              AppImages.membership,
              fit: BoxFit.cover,
              color: AppColors.appBlack,
            ),
          ),
        ),

        ///Cart
        Visibility(
          visible: (AppConstants.appData.isUserView ?? false) && isCartVisible,
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () async {
              //If web view
              // if (kIsWeb) {
              //   return await CommonMethods.appDownloadDialog();
              // }
              // //Else if static user then open login screen
              // else
              if (CommonMethods().isStaticUser()) {
                return CommonMethods().goToSignUpFlow();
              }

              ///If on tap cart is not empty then go to shopping cart else call the onTapCart method

              else if (onTapCart == null) {
                var screen = const ShoppingCartScreen();
                var route = MaterialPageRoute(builder: (context) => screen);
                Navigator.push(context, route).then((value) {
                  //Refresh cart
                  AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
                });
                return;
              } else {
                onTapCart();

                //Refresh cart
                AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
              }
            },
            child: StreamBuilder<bool>(
                stream: AppConstants.bottomNavigationRefreshCtrl.stream,
                builder: (context, snapshot) {
                  return badges.Badge(
                    showBadge: shoppingCartQuantityDataModel
                        .productReferenceList.isNotEmpty,
                    // padding: const EdgeInsets.all(5),
                    badgeStyle: BadgeStyle(badgeColor: AppColors.brandBlack),
                    badgeContent: Text(
                      shoppingCartQuantityDataModel
                                  .productReferenceList.length >
                              9
                          ? "9+"
                          : shoppingCartQuantityDataModel
                              .productReferenceList.length
                              .toString(),
                      style: TextStyle(
                          color: AppColors.appWhite,
                          fontSize: 10,
                          fontWeight: FontWeight.w700),
                    ),
                    //alignment: Alignment.center,
                    // position: BadgePosition.topStart(start: 7),
                    position: badges.BadgePosition.topEnd(top: -9, end: -10),
                    child: Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
                      child: SvgPicture.asset(
                        AppImages.cartOutlined,
                        height: 28,
                        color: AppColors.appBlack,
                      ),
                    ),
                  );
                }),
          ),
        ),

        ///Drawer
        // Visibility(
        //   ///Un-comment
        //   // visible: isDrawerVisible,
        //   visible: false,
        //   child: CupertinoButton(
        //       padding: EdgeInsets.zero,
        //       onPressed: () {
        //         onTapDrawer();
        //       },
        //       child: SvgPicture.asset(AppImages.drawerIcon)),
        // ),

        ///Text button
        Visibility(
          visible: isTextButtonVisible,
          child: CupertinoButton(
              onPressed: () {
                onTapTextButton();
              },
              child: textButtonWidget),
        ),

        ///Dropdown icon
        Visibility(
          visible: isDropdownVisible,
          child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                onTapDropdown();
              },
              child: RotatedBox(
                  quarterTurns: isRotateDropdownIcon ? 2 : 0,
                  child: SizedBox(
                      height: 16,
                      width: 16,
                      child: SvgPicture.asset(
                        AppImages.downArrow2,
                        color: AppColors.appBlack,
                      )))),
        ),

        ///Filter icon
        Visibility(
          visible: isFilterVisible,
          child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                onTapFilter();
              },
              child: RotatedBox(
                  quarterTurns: isRotateDropdownIcon ? 2 : 0,
                  child: SvgPicture.asset(AppImages.filter))),
        ),

        ///Default Menu
        Visibility(
          visible: isDefaultMenuVisible,
          child: PopupMenuButton(
            // shadowColor: Colors.transparent,
            // add icon, by default "3 dot" icon
            // icon: Icon(Icons.book)
            padding: EdgeInsets.zero,
            icon: SvgPicture.asset(AppImages.drawerIcon,
                color: AppColors.appBlack),
            itemBuilder: (context) {
              return [
                ///Report
                PopupMenuItem<int>(
                  height: 0,
                  value: 0,
                  onTap: () {
                    if (onTapReport == null) {
                      //If non register user
                      if (CommonMethods().isStaticUser()) {
                        CommonMethods().goToSignUpFlow();
                        return;
                      }
                      //On tap open a dialog
                      showDialog<String>(
                        context: context,
                        builder: (BuildContext context) => const AlertDialog(
                          content: IssueSuggestionDialog(),
                        ),
                      );
                      // //User view
                      // AppConstants.userLevelPersistentTabController.jumpToTab(4);
                      // //Store view
                      // AppConstants.storeLevelPersistentTabController.jumpToTab(4);
                    } else {
                      onTapReport();
                    }
                  },
                  padding: EdgeInsets.zero,
                  child: Container(
                    width: 150,
                    padding: const EdgeInsets.all(10),
                    child: AppCommonWidgets.menuText(text: AppStrings.report),
                    // child: appText(AppStrings.report, fontFamily: AppConstants.rRegular, fontSize: 14, fontWeight: FontWeight.w700),
                  ),
                ),
              ];
            },
          ),
        ),
        //
        ///Custom Menu
        Visibility(
          visible: isCustomMenuVisible,
          child: customMenuButton,
        ),

        // isDrawerVisible?const SizedBox():const
        const SizedBox(
          width: 15,
        ),
      ],

      //endregion
    );
  }

  //endregion

  //region Appbar title text
  static Widget appBarTitleText({required String text}) {
    return Text(
      text,
      style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
    );
  }

  //endregion

  //region Appbar Text button text
  static Widget appBarTextButtonText({required String text}) {
    return Container(
      constraints: const BoxConstraints(
        minHeight: 24, // Minimum height to prevent cutting
      ),
      child: Text(
        text,
        style: AppTextStyle.access1(textColor: AppColors.brandBlack),
        textAlign: TextAlign.center,
        overflow: TextOverflow.visible, // Allow text to be fully visible
      ),
    );
  }

  //endregion

  //region Empty text
  static Widget emptyResponseText({required String emptyMessage}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Text(
        emptyMessage,
        textAlign: TextAlign.center,
        style: AppTextStyle.smallText(textColor: AppColors.writingBlack0),
      ),
    );
  }

  //endregion

  //region Error text
  static Widget errorMessage({required String error}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Text(
        error,
        textAlign: TextAlign.center,
        style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
      ),
    );
  }

  //endregion

  //region Error widget
  static Widget errorWidget(
      {String errorMessage = AppStrings.notLoaded,
      required VoidCallback onTap,
      double? height,
      double width = double.infinity}) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Container(
        height: height,
        alignment: Alignment.center,
        margin: const EdgeInsets.all(10),
        decoration: BoxDecoration(
            // color: AppColors.textFieldFill1,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: AppColors.borderColor1)),
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              AppImages.refreshIcon,
              height: 40,
              width: 40,
            ),
            verticalSizedBox(10),
            Text(
              "$errorMessage, refresh",
              textAlign: TextAlign.center,
              style: AppTextStyle.smallText(textColor: AppColors.appBlack),
            )
          ],
        ),
      ),
    );
  }

  //endregion

  //region App circular progress
  static Widget appCircularProgress({bool isPaginationProgress = false}) {
    return Center(
      child: SpinKitFadingCircle(
        color: isPaginationProgress
            ? AppColors.writingBlack1
            : AppColors.brandBlack, // Color of the spinning circle.
      ),
      // child: CircularProgressIndicator(
      //   color: AppColors.brandGreen,
      // ),
    );
  }

  //endregion

  static Widget inlineCircularProgress({
    bool isPaginationProgress = false,
    double size = 24.0,
  }) {
    return SizedBox(
      height: size,
      width: size,
      child: SpinKitFadingCircle(
        color: isPaginationProgress
            ? AppColors.writingBlack1
            : AppColors.brandBlack,
        size: size,
      ),
    );
  }
//endregion

  //region Bottom List Space
  static Widget bottomListSpace({required BuildContext context}) {
    return verticalSizedBox(MediaQuery.of(context).size.height / 5);
  }

  //endregion

  ///Text styles
  //region Referral code style
  static TextStyle referralTextStyle() {
    return TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: AppColors.writingBlack,
      letterSpacing: 4,
    );
  }

//endregion

//region Widget flag and text
  static Widget flagAndText(
      {required String text, required BuildContext context}) {
    return Padding(
      padding: const EdgeInsets.only(top: 102, bottom: 77),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            AppImages.appIcon,
            // height: MediaQuery.of(context).size.height * 0.1,
            // width: MediaQuery.of(context).size.height * 0.1,
            height: 216,
            width: 216,
          )
          // const SizedBox(height: 32),
          // Text(text,
          //   textAlign: TextAlign.center,
          //   style: AppTextStyle.introSlideTitle(textColor: AppColors.brandGreen,fontSize: 28),),
          // // Text(
          // //   text,
          // //   maxLines: 10,
          // //   textAlign: TextAlign.center,
          // //   style: const TextStyle(fontFamily: AppConstants.rRegular, fontSize: 24, fontWeight: FontWeight.w700, color: AppColors.appBlack),
          // // ),
        ],
      ),
    );
  }

//endregion

  ///Product card in grid view
//region Product card in grid
  static Widget productCardInGrid({
    required Product product,
    required double screenWidth,
    required BuildContext context,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ///Image
        SizedBox(
          height: screenWidth / 2,
          width: screenWidth / 2,
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10)),
                child: product.prodImages?.isNotEmpty == true
                    ? extendedImage(
                        product.prodImages!.first.productImage ?? '',
                        context,
                        1000,
                        1000,
                        customPlaceHolder: AppImages.productPlaceHolder,
                        fit: BoxFit.cover,
                        imageHeight: screenWidth / 2,
                        imageWidth: screenWidth / 2,
                      )
                    : SvgPicture.asset(
                        AppImages.productPlaceHolder,
                        fit: BoxFit.cover,
                        height: screenWidth / 2,
                        width: screenWidth / 2,
                      ),
              ),
              if (product.variants?.every((variant) => variant['stock'] == 0) ??
                  false)
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    decoration: BoxDecoration(
                      color: AppColors.appWhite,
                      borderRadius: BorderRadius.circular(4),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.appBlack.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Text(
                      'Out of Stock',
                      style: AppTextStyle.smallText(
                        textColor: AppColors.red,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),

        ///Product detail
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(5),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                /// Brand and product name in an IntrinsicHeight widget to handle dynamic height
                IntrinsicHeight(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                        child: RichText(
                          textScaleFactor: MediaQuery.textScaleFactorOf(
                              AppConstants.globalNavigator.currentContext!),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: '${product.brandName ?? ''} ',
                                style: AppTextStyle.contentHeading0(
                                    textColor: AppColors.appBlack),
                              ),
                              TextSpan(
                                text: product.productName ?? '',
                                style: AppTextStyle.contentText0(
                                    textColor: AppColors.appBlack),
                              ),
                            ],
                          ),
                        ),
                      ),

                      /// Selling and MRP prices
                      Padding(
                        padding: const EdgeInsets.only(top: 2.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              "₹ ${_getDisplaySellingPrice(product).toStringAsFixed(2)}",
                              style: AppTextStyle.access0(
                                  textColor: AppColors.appBlack),
                            ),
                            horizontalSizedBox(10),
                            Text(
                              "₹ ${_getDisplayMrpPrice(product).toStringAsFixed(2)}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyle.access0(
                                  textColor: AppColors.writingBlack1,
                                  isLineThrough: true),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

//endregion

  ///Store card grid
  //region Store card grid
  static Widget storeCardGreed(
      {required StoreInfo storeInfo,
      required onTap,
      required BuildContext context,
      double width = 100}) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        onTap();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CustomImageContainer(
            height: width / 5,
            width: width / 5,
            imageUrl: storeInfo.icon,
            imageType: CustomImageContainerType.store,
            showShadow: false,
          ),
          // Container(
          //   height: width / 5,
          //   width: width / 5,
          //   decoration: BoxDecoration(
          //     color: AppColors.appWhite,
          //     // color: AppColors.appWhite,
          //     borderRadius: BorderRadius.circular(20),
          //     boxShadow: AppColors.appShadow,
          //   ),
          //   child: ClipRRect(
          //       borderRadius: BorderRadius.circular(20),
          //       child: storeInfo.icon == null
          //           ? SvgPicture.asset(
          //               AppImages.storePlaceHolder,
          //               height: 30,
          //               width: 30,
          //               fit: BoxFit.cover,
          //             )
          //           : extendedImage(storeInfo.icon!, context, 200, 200,
          //               customPlaceHolder: AppImages.storePlaceHolder,
          //               fit: BoxFit.cover,
          //               imageHeight: width / 5,
          //               imageWidth: width / 5)),
          // ),
          verticalSizedBox(8),
          Container(
            color: Colors.transparent,
            // padding: const EdgeInsets.symmetric(horizontal: 3),
            alignment: Alignment.center,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    storeInfo.storehandle!,
                    maxLines: 1,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.subTitle(textColor: AppColors.appBlack)
                        .copyWith(height: 1.2),
                  ),
                ),
                VerifiedBadge(
                  width: 15,
                  height: 15,
                  subscriptionType: storeInfo.subscriptionType,
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  //endregion

  ///Product view

//region Single Tab
  static Widget singleTab({required String name}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            name,
            style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
          ),
        ),
        SizedBox(width: double.infinity, child: divider()),
      ],
    );
  }

//endregion

  ///Copy and call button
  //region Copy and call
  static Widget copyCallButton({required String text, required onTap}) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        alignment: Alignment.center,
        height: 25,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(100),
            color: AppColors.textFieldFill1),
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Text(
          text,
          style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: AppConstants.rRegular,
              color: AppColors.writingColor3),
        ),
      ),
    );
  }

//endregion

  ///Order buttons
//region Active button
  static Widget activeButton({
    required String buttonName,
    required void Function() onTap, // Use a specific function type for onTap.
    Color? buttonColor,
    Color? textColor,
  }) {
    buttonColor ??= AppColors.brandBlack;
    textColor ??= AppColors.appWhite;
    return CupertinoButton(
      onPressed: onTap,
      padding: EdgeInsets.zero,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 10),
        decoration: BoxDecoration(
          color: buttonColor,
          borderRadius: BorderRadius.circular(100),
        ),
        child: Center(
          child: Text(
            buttonName,
            style: AppTextStyle.access0(textColor: textColor),
          ),
        ),
      ),
    );
  }

//endregion

//region Active button
  static Widget activeButtonWithArrow({
    required String buttonName,
    required void Function() onTap, // Use a specific function type for onTap.
    Color? buttonColor,
  }) {
    buttonColor ??= AppColors.brandBlack;
    return CupertinoButton(
      onPressed: onTap,
      padding: EdgeInsets.zero,
      child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 10),
          decoration: BoxDecoration(
            color: buttonColor,
            borderRadius: BorderRadius.circular(100),
          ),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
            decoration: BoxDecoration(
              color: buttonColor,
              borderRadius: BorderRadius.circular(100),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      buttonName,
                      style:
                          AppTextStyle.access0(textColor: AppColors.appWhite),
                    ),
                  ),
                ),
                Icon(Icons.arrow_forward_ios,
                    size: 16, color: AppColors.appWhite),
                const SizedBox(
                  width: 10,
                )
              ],
            ),
          )),
    );
  }

  //region Menu text
  static Widget menuText({required String text}) {
    return Text(
      text,
      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
    );
  }

  //endregion

  //region Sub order info
  static Widget subOrderInfo(
      {required SubOrder subOrder,
      dynamic onTap,
      required BuildContext context,
      bool isCheckBoxVisible = true,
      bool isPriceDetailVisible = true,
      bool isStatusVisible = true,
      bool isArrowVisible = false}) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //Product name and image
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Visibility(
                  visible: isCheckBoxVisible,
                  child: Container(
                    margin: const EdgeInsets.only(right: 10),
                    padding: const EdgeInsets.all(3),
                    height: 24,
                    width: 24,
                    child: Checkbox(
                      value: subOrder.isSelected,
                      activeColor: AppColors.brandGreen,
                      onChanged: (value) {
                        onTap();
                      },

                      //
                      // fillColor: MaterialStateColor.resolveWith((states) {
                      //   if (states.contains(MaterialState.selected)) {
                      //     return Colors.blue; // Color when checkbox is checked
                      //   } else {
                      //     return Colors.grey; // Color when checkbox is unchecked
                      //   }
                      // }),
                    ),
                  ),
                  // child: appRadioCheckBox(isActive:subOrder.isSelected,text:"",
                  //     isRadio: false,
                  //
                  //     isExpand: false, onTap:(){
                  //
                  //   // startShippingBloc.onSelectDeliveryMethod(true);
                  //
                  // }),
                ),
                Expanded(
                  child: RichText(
                    textScaleFactor: MediaQuery.textScaleFactorOf(
                        AppConstants.globalNavigator.currentContext!),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: subOrder.productBrand,
                          style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        TextSpan(
                            text: " ${subOrder.productName}",
                            // text:"Nykaa Matte Nail Lacquer & Nail Enamel 2 in 1 coloNykaa Matte Nail Lacquer & Nail Enamel 2 in 1 colors with...rs with",
                            style: AppTextStyle.contentText0(
                              textColor: AppColors.appBlack,
                            )),
                        // WidgetSpan(child: Text(productName,
                        // maxLines: 1,
                        // ))
                      ],
                    ),
                  ),
                ),
                horizontalSizedBox(50),
                ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(6)),
                  child: Container(
                    height: 35,
                    width: 35,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(6))),
                    child: extendedImage(
                      subOrder.productImage,
                      context,
                      100,
                      100,
                      cache: true,
                    ),
                  ),
                ),
                //Arrow
                Visibility(
                  visible: isArrowVisible,
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Container(
                      margin: const EdgeInsets.only(left: 15),
                      height: 25,
                      width: 25,
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.keyboard_arrow_right,
                        color: AppColors.appBlack,
                        weight: 2,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            verticalSizedBox(5),
            //Delivery fee and price
            Visibility(
              visible: isPriceDetailVisible,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "₹${subOrder.sellingPrice} X ${subOrder.productQuantity} = ₹${subOrder.sellingPrice! * subOrder.productQuantity!}",
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                  horizontalSizedBox(15),
                  Text(
                    "${AppStrings.delivery}: ${subOrder.suborderFeeDetails!.productLevelDeliveryFee != 0 ? subOrder.suborderFeeDetails!.productLevelDeliveryFee == 0 ? "₹${subOrder.suborderFeeDetails!.storeLevelDeliveryFee}" : "₹${subOrder.suborderFeeDetails!.productLevelDeliveryFee}" : "₹0"}",
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                  horizontalSizedBox(15),
                  Text(
                      "${AppStrings.returnText}: ${subOrder.returnDays == 0 ? AppStrings.noReturn : "${subOrder.returnDays} days"}",
                      style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack,
                      ))
                ],
              ),
            ),
            verticalSizedBox(5),
            // Variant information display
            OrderVariantDisplayWidget(
              variantDetails: subOrder.variantDetails,
              showPriceInfo: false,
            ),
            //Status
            Visibility(
              visible: isStatusVisible,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  // color: AppColors.lightestGrey
                ),
                margin: const EdgeInsets.only(top: 10),
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                child: Text(
                  CommonMethods.subOrderStatusToString(
                      subOrderStatus: subOrder.suborderStatus!),
                  style: AppTextStyle.smallText(textColor: AppColors.appBlack),
                ),
              ),
            )
            // subOrderTag(AppStrings.confirmedNotYetShipped)
          ],
        ),
      ),
    );
  }

  //endregion

//region In-active button
  static Widget inActiveButton(
      {required String buttonName, required dynamic onTap}) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 10),
        decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
            borderRadius: BorderRadius.circular(100)),
        child: Center(
          child: Text(
            buttonName,
            style: AppTextStyle.access0(textColor: AppColors.appBlack),
          ),
        ),
        // child: Text(
        //   buttonName,
        //   textAlign: TextAlign.center,
        //   style: AppTextStyle.access0(textColor: AppColors.appBlack),
        // ),
      ),
    );
  }

//endregion

  ///Sub order buttons
//region Sub- order buttons
  static Widget subOrderButton(
      {required String buttonName,
      required dynamic onTap,
      Color? textColor,
      Color? buttonColor,
      double horizontalPadding = 10}) {
    textColor ??= AppColors.appBlack;
    buttonColor ??= AppColors.textFieldFill1;
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        padding:
            EdgeInsets.symmetric(vertical: 10, horizontal: horizontalPadding),
        decoration: BoxDecoration(
            color: buttonColor, borderRadius: BorderRadius.circular(10)),
        child: AppAutoScrollableText(
          text: buttonName,
          textStyle: AppTextStyle.access0(textColor: textColor),
        ),
        // child: Text(
        //   buttonName,
        //   style: AppTextStyle.access0(textColor: textColor),
        // ),
      ),
    );
  }

//endregion

  ///App Buttons
//region Border button
  static Widget borderButton({
    required String buttonName,
    required dynamic onTap,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        onTap();
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(100),
            border: Border.all(color: AppColors.appBlack, width: 1.3)),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Text(
          buttonName,
          style: AppTextStyle.access0(textColor: AppColors.appBlack),
        ),
      ),
    );
  }

//endregion

  //region Setting Options
  static Widget settingOption(
      {required String optionText,
      Widget? subOption,
      required onTap,
      String? prefixIcon,
      Color? arrowColor,
      Color? optionTextColor,
      double prefixIconPadding = 0,
      double horizontalPadding = 0,
      bool isArrowVisible = true,
      bool isDoneVisible = false}) {
    arrowColor ??= AppColors.appBlack;
    optionTextColor ??= AppColors.appBlack;
    return InkWell(
      // padding: EdgeInsets.zero,
      onTap: isArrowVisible
          ? () {
              onTap();
            }
          : null,
      child: Container(
        margin:
            EdgeInsets.symmetric(vertical: 8, horizontal: horizontalPadding),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            //Prefix icon
            prefixIcon != null
                ? Container(
                    padding: EdgeInsets.all(prefixIconPadding),
                    margin: const EdgeInsets.only(right: 10),
                    height: 36,
                    width: 36,
                    child: prefixIcon.endsWith("svg")
                        ? ThemedImageIcon.asset(
                            prefixIcon,
                            fit: BoxFit.fill,
                            // color: AppColors.appBlack,
                          )
                        : ThemedImageIcon.asset(
                            prefixIcon,
                            height: 36,
                            width: 36,
                            fit: BoxFit.fill,
                          ))
                : const SizedBox(
                    height: 36,
                  ),
            //Option text with check mark
            Row(
              children: [
                Text(
                  optionText,
                  style: AppTextStyle.access1(textColor: optionTextColor)
                      .copyWith(height: 0),
                ),
                Visibility(
                  visible: isDoneVisible,
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    height: 24,
                    width: 24,
                    child: Image.asset(
                      AppImages.checkListDone,
                      height: 24,
                      width: 24,
                    ),
                  ),
                ),
              ],
            ),

            //Sub-option
            Expanded(
              child: Visibility(
                  visible: subOption != null,
                  child: Container(
                    alignment: Alignment.centerRight,
                    margin: const EdgeInsets.only(left: 20),
                    child: subOption,
                  )),
            ),
            //Arrow
            Visibility(
              visible: isArrowVisible,
              child: RotatedBox(
                  quarterTurns: 3,
                  child: SvgPicture.asset(
                    AppImages.arrow,
                    color: arrowColor,
                    // height: 20,
                    // width: 20,
                  )),
            ),
          ],
        ),
      ),
    );
  }

//endregion

//region Drop down options
  static Widget dropDownOptions(
      {required onTap,
      Color? arrowColor,
      double border = 8.0,
      bool isArrowVisible = true,
      TextAlign textAlign = TextAlign.start,
      required String hintText,
      required String? value,
      required BuildContext context}) {
    arrowColor ??= AppColors.appBlack;
    return InkWell(
      onTap: () {
        onTap();
      },
      child: SizedBox(
        // height: 42,
        child: Stack(
          alignment: Alignment.center,
          children: [
            AppTextFields.allTextField(
                textAlign: textAlign,
                enabled: false,
                context: context,
                textEditingController: TextEditingController(text: value),
                hintText: hintText,
                border: border),
            Visibility(
              visible: isArrowVisible,
              child: Positioned(
                right: 0,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: SvgPicture.asset(
                    AppImages.arrow,
                    color: arrowColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

//endregion

//region Scrollable text
  static Widget scrollableText(
      {required String text, required TextStyle textStyle}) {
    return Marquee(
      text: text,

      style: textStyle,
      scrollAxis: Axis.horizontal,
      crossAxisAlignment: CrossAxisAlignment.start,
      blankSpace: 20.0,
      velocity: 50.0,
      // pauseAfterRound:  Duration.zero,
      startPadding: 50.0,
      // accelerationDuration: Duration.zero,
      // accelerationCurve: Curves.slowMiddle,
      // decelerationDuration: const Duration(seconds: 5),
      decelerationCurve: Curves.slowMiddle,
    );
  }

//endregion

//region Order title
  static Widget orderTitleText(
      {required BuildContext context,
      required String text,
      bool isVisibleExclamation = true}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 13),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
            ),
            // child: appText("Self delivery will require OTP verification from customer. ",
            //     fontSize: 16, fontWeight: FontWeight.w600, color: AppColors.writingColor2, maxLine: 3, fontFamily: AppConstants.rRegular),
            //
          ),
          Visibility(
              visible: isVisibleExclamation,
              child: SvgPicture.asset(AppImages.exclamation))
        ],
      ),
    );
  }

//endregion
//region Valid and invalid
  static Widget validAndInvalid({
    bool isUnderLine = false,
    double paddingOnTop = 10,
    dynamic onTap,
    required Color textColor,
    required String buttonText,
    String? secondaryText,
    Color? secondaryTextColor,
    bool primaryTextUnderline = false,
  }) {
    return Container(
      padding: EdgeInsets.only(top: paddingOnTop),
      child: InkWell(
        onTap: () {
          if (onTap != null) {
            onTap();
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Wrap(
              spacing: 4.0, // space between texts
              children: [
                Text(
                  buttonText,
                  style: AppTextStyle.smallText(
                    textColor: textColor,
                    isUnderline: primaryTextUnderline,
                  ),
                  softWrap: true,
                  overflow: TextOverflow.visible,
                ),
                if (secondaryText != null)
                  Text(
                    secondaryText,
                    style: AppTextStyle.smallText(
                      textColor: secondaryTextColor ?? textColor,
                      isUnderline: isUnderLine,
                    ),
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

//endregion

//region Sort button
  static Widget sortButtons(
      {required String buttonName, required bool isSelected}) {
    return Container(
      alignment: Alignment.center,
      width: 100,
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
          color: isSelected ? AppColors.brandBlack : AppColors.appWhite,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.writingColor3, width: 0.5)),
      child: Text(
        buttonName,
        style: AppTextStyle.contentText0(
            textColor: isSelected ? AppColors.appWhite : AppColors.appBlack),
      ),
      // child: appText(
      //   buttonName,
      //   fontFamily: AppConstants.rRegular,
      //   fontSize: 14,
      //   fontWeight: FontWeight.w400,
      //   color: AppColors.writingColor2,
      // ),
    );
  }

//endregion

//region File placeholder
  static String filePlaceHolder({required String filePath}) {
    // Convert the file name to lowercase for case-insensitive matching
    String extension = filePath.toLowerCase();

    // Check if it's a document file
    if (extension.endsWith('.pdf')) {
      return AppImages.pdfPlaceHolder;
    }
    //Doc
    if (extension.endsWith('.doc') || extension.endsWith('.docx')) {
      return AppImages.docxPlaceHolder;
    }
    //Text
    if (extension.endsWith('.txt')) {
      return AppImages.textPlaceHolder;
    }

    // Check if it's a video file
    if (extension.endsWith('.mp4') ||
        extension.endsWith('.avi') ||
        extension.endsWith('.mov')) {
      return AppImages.videoPlaceHolder;
    }

    return AppImages.pdfPlaceHolder;
  }

//endregion

  ///Shadow decoration
  static Decoration shadowDecoration() {
    return BoxDecoration(
      color: AppColors.appWhite,
      borderRadius: const BorderRadius.all(Radius.circular(10)),
      boxShadow: [
        BoxShadow(
          offset: const Offset(0, 0.5),
          blurRadius: 2,
          color: AppColors.appBlack.withOpacity(0.2),
        ),
      ],
    );
  }

  //region Unlock feature
  static Widget unlockFeature() {
    return Positioned.fill(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 8, sigmaY: 10),
        child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            alignment: Alignment.center,
            height: double.infinity,
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  AppStrings.unlockAndSeamless,
                  textAlign: TextAlign.center,
                  style:
                      AppTextStyle.pageHeading(textColor: AppColors.appBlack),
                ),
                const SizedBox(
                  height: 20,
                ),
                CupertinoButton(
                    color: AppColors.brandBlack,
                    child: Text(AppStrings.downloadApp),
                    onPressed: () {})
              ],
            )),
      ),
    );
  }

//endregion

//region Menu card
  static Widget menuCard({required String text}) {
    return Container(
        // width: 250,
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.orange, // Orange color for the bottom side
              width: 3, // Width of the border (adjust as needed)
            ),
          ),
        ),
        child: AppCommonWidgets.menuText(text: text));
  }
//endregion

  // Helper methods to get display prices from product
  static double _getDisplaySellingPrice(Product product) {
    // // First check if product has direct selling price
    // if (product.sellingPrice != null && product.sellingPrice! > 0) {
    //   return product.sellingPrice!.toDouble();
    // }

    // Then check variants
    if (product.variants?.isNotEmpty == true) {
      try {
        final variant = ProductVariant.fromJson(product.variants!.first);
        return variant.sellingPrice.toDouble();
      } catch (e) {
        return 0.0;
      }
    }

    return 0.0;
  }

  static double _getDisplayMrpPrice(Product product) {
    // // First check if product has direct mrp price
    // if (product.mrpPrice != null && product.mrpPrice! > 0) {
    //   return product.mrpPrice!.toDouble();
    // }

    // Then check variants
    if (product.variants?.isNotEmpty == true) {
      try {
        final variant = ProductVariant.fromJson(product.variants!.first);
        return variant.mrpPrice.toDouble();
      } catch (e) {
        return 0.0;
      }
    }

    return 0.0;
  }
}
