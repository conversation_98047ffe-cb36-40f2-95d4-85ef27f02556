import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/model/seller_all_order_response/seller_order_details.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class TotalAmountToBeReceivedCommonWidgets {
  //region  title
  static Widget title({required PriceDetails priceDetails}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Row(
              children: [
                Visibility(
                    visible: priceDetails.orderBreakupItemText!
                        .toLowerCase()
                        .contains("flash"),
                    child: Image.asset(
                      AppImages.flash,
                      height: 25,
                      width: 25,
                    )),
                Visibility(
                    visible: priceDetails.orderBreakupItemText!
                        .toLowerCase()
                        .contains("infinity"),
                    child: Image.asset(
                      AppImages.infinity,
                      height: 25,
                      width: 25,
                    )),
                Expanded(
                  child: Text(
                    priceDetails.orderBreakupItemText ?? "",
                    maxLines: 4,
                    overflow: TextOverflow.visible, // ensures no overflow
                    style: (priceDetails.orderBreakupItemSubtext ==
                                "CURRENT_AMOUNT_DISCLAIMER" ||
                            priceDetails.orderBreakupItemSubtext ==
                                "CURRENT_BREAKUP_DISCLAIMER")
                        ? AppTextStyle.robotoItalicStyle(
                            textColor: AppColors.textFieldFill3,
                          )
                        : AppTextStyle.settingText(
                            textColor: AppColors.appBlack,
                          ),
                  ),
                ),
              ],
            ),
          ),
          horizontalSizedBox(50),
          Text(
            priceDetails.orderBreakupItemValue ?? "",
            style: AppTextStyle.settingText(textColor: AppColors.appBlack),
          ),
        ],
      ),
    );
  }
  //endregion

  //region Sub title
  static Widget subTitle({required PriceDetails priceDetails}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                priceDetails.orderBreakupItemText!,
                style:
                    AppTextStyle.heading2Medium(textColor: AppColors.appBlack),
              ),
              Text(
                priceDetails.orderBreakupItemValue!,
                style:
                    AppTextStyle.heading2Medium(textColor: AppColors.appBlack),
              ),
            ],
          ),
          priceDetails.orderBreakupItemSubtext == null
              ? const SizedBox()
              : Container(
                  margin: const EdgeInsets.only(top: 3),
                  child: Text(CommonMethods.addRupeeSymbol(
                      input: priceDetails.orderBreakupItemSubtext!)))
        ],
      ),
    );
  }
//endregion
}
