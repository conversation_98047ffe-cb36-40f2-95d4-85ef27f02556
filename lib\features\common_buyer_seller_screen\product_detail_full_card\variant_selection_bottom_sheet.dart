import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class VariantSelectionBottomSheet extends StatefulWidget {
  final Product product;
  final ProductDetailFullCardBloc productDetailFullCardBloc;
  final Function(ProductVariant) onVariantSelected;

  const VariantSelectionBottomSheet({
    Key? key,
    required this.product,
    required this.productDetailFullCardBloc,
    required this.onVariantSelected,
  }) : super(key: key);

  @override
  State<VariantSelectionBottomSheet> createState() =>
      _VariantSelectionBottomSheetState();
}

class _VariantSelectionBottomSheetState
    extends State<VariantSelectionBottomSheet> {
  ProductVariant? selectedVariant;
  Map<String, String> selectedOptions = {};
  bool _showVariantList = true;

  @override
  void initState() {
    super.initState();
    selectedVariant = widget.productDetailFullCardBloc.selectedVariant;
    if (selectedVariant != null) {
      selectedOptions = Map.from(selectedVariant!.combinations);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            height: 4,
            width: 40,
            decoration: BoxDecoration(
              color: AppColors.borderColor1,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Container(
            padding: const EdgeInsets.all(16),
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Price information or availability message
                _buildPriceSection(),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _showVariantList
                            ? "All product variants"
                            : "Select product options",
                        style:
                            AppTextStyle.access0(textColor: AppColors.appBlack),
                      ),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _showVariantList = !_showVariantList;
                        });
                      },
                      child: Icon(
                        _showVariantList ? Icons.view_list : Icons.view_list,
                        color: AppColors.appBlack,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Body - options or variant list
          _showVariantList
              ? _buildVariantListSection()
              : _buildOptionsSection(),

          // Done button
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            child: ElevatedButton(
              onPressed: () {
                // If a variant is selected, use it
                if (selectedVariant != null) {
                  widget.onVariantSelected(selectedVariant!);
                }
                // Close the bottom sheet
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.appBlack,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                "Done",
                style: AppTextStyle.contentHeading0(textColor: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSection() {
    // Check if any options are selected
    bool hasSelectedOptions = selectedOptions.isNotEmpty;

    // Check if all options are selected
    bool allOptionsSelected = widget.product.options != null &&
        widget.product.options!.keys
            .every((optionName) => selectedOptions.containsKey(optionName));

    if (!hasSelectedOptions) {
      // Show default message when no options are selected
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Selling price",
            style: AppTextStyle.smallText(textColor: AppColors.appBlack),
          ),
          const SizedBox(height: 4),
          Text(
            "Select options to see price",
            style:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          ),
        ],
      );
    }

    // if (!allOptionsSelected) {
    //   // Show message when not all options are selected
    //   return Column(
    //     crossAxisAlignment: CrossAxisAlignment.start,
    //     children: [
    //       Text(
    //         "Selling price",
    //         style: AppTextStyle.smallText(textColor: AppColors.appBlack),
    //       ),
    //       const SizedBox(height: 4),
    //       Text(
    //         "Select all options to see price",
    //         style:
    //             AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
    //       ),
    //     ],
    //   );
    // }

    if (selectedVariant == null) {
      // Show "combination not available" message
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          Text(
            "Combination is not available yet",
            style: AppTextStyle.contentText0(textColor: AppColors.red),
          ),
        ],
      );
    }

    // Show actual variant pricing
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Selling price",
          style: AppTextStyle.access0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 4),
        Row(
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Text(
              "₹${_getSelectedVariantSellingPrice()}",
              style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
            ),
            const SizedBox(width: 8),
            if (_getSelectedVariantMrpPrice() !=
                _getSelectedVariantSellingPrice())
              Text(
                "₹${_getSelectedVariantMrpPrice()}",
                style: AppTextStyle.access0(
                  textColor: AppColors.writingBlack1,
                  isLineThrough: true,
                ),
              ),
            const SizedBox(width: 8),
            if (_getSelectedVariantMrpPrice() !=
                _getSelectedVariantSellingPrice())
              Text(
                "${_getDiscountPercentage()}% OFF",
                style: AppTextStyle.access0(textColor: AppColors.brandGreen),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          _getSelectedVariantStock() > 10
              ? "${_getSelectedVariantStock()} units available"
              : _getSelectedVariantStock() > 0
                  ? "Only ${_getSelectedVariantStock()} units available"
                  : "Out of stock",
          style: AppTextStyle.smallText(
            textColor: _getSelectedVariantStock() > 10
                ? AppColors.appBlack
                : _getSelectedVariantStock() > 0
                    ? AppColors.orange
                    : AppColors.red,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildOptionSelectors() {
    List<Widget> widgets = [];

    if (widget.product.options != null) {
      widget.product.options!.forEach((optionName, optionValues) {
        widgets.add(_buildOptionSelector(optionName, optionValues));
        widgets.add(const SizedBox(height: 16));
      });
    }

    return widgets;
  }

  Widget _buildOptionSelector(String optionName, List<dynamic> optionValues) {
    // Get all variants
    final variants =
        widget.productDetailFullCardBloc.getVariantsWithCombinations();

    // Get all unique values for this option that exist in variants
    final Set<String> existingValues = {};
    for (final variant in variants) {
      if (variant.combinations.containsKey(optionName)) {
        existingValues.add(variant.combinations[optionName]!);
      }
    }

    // Filter and sort optionValues based on the order in optionValues but only include existing values
    final filteredAndOrderedValues = optionValues
        .map((e) => e.toString())
        .where(existingValues.contains)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          optionName,
          style: AppTextStyle.smallText(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 8),
        LayoutBuilder(
          builder: (context, constraints) {
            final buttonWidth = (constraints.maxWidth - 8 * (3 - 1)) /
                3; // 3 buttons per row with 8px spacing
            return Wrap(
              spacing: 8,
              runSpacing: 8,
              children: filteredAndOrderedValues.map((value) {
                final stringValue = value.toString();
                final isSelected = selectedOptions[optionName] == stringValue;
                final isAvailable =
                    _isOptionValueAvailable(optionName, stringValue);
                final isCompatible =
                    _isOptionValueAvailableWithCurrentSelection(
                        optionName, stringValue);

                return GestureDetector(
                  onTap: () => _selectOption(optionName, stringValue),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? isAvailable
                              ? AppColors.appBlack
                              : AppColors.red.withOpacity(0.2)
                          : Colors.white,
                      border: Border.all(
                        color: isSelected
                            ? isAvailable
                                ? AppColors.appBlack
                                : AppColors.red.withOpacity(0.7)
                            : isCompatible
                                ? AppColors.appBlack
                                : AppColors.borderColor1,
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Opacity(
                      opacity: isCompatible ? 1.0 : 0.2,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (isSelected)
                            Icon(
                              Icons.check,
                              size: 16,
                              color: Colors.white,
                            ),
                          if (isSelected) const SizedBox(width: 4),
                          Text(
                            stringValue,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.smallText(
                              textColor: isSelected
                                  ? Colors.white
                                  : AppColors.appBlack,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            );
          },
        ),
      ],
    );
  }

  void _selectOption(String optionName, String optionValue) {
    setState(() {
      // Check if this option is already selected
      if (selectedOptions.containsKey(optionName) &&
          selectedOptions[optionName] == optionValue) {
        // If already selected, deselect it
        selectedOptions.remove(optionName);
      } else {
        // Otherwise, select it
        selectedOptions[optionName] = optionValue;
      }
      _updateSelectedVariant();
    });
  }

  void _updateSelectedVariant() {
    final variants =
        widget.productDetailFullCardBloc.getVariantsWithCombinations();

    for (final variant in variants) {
      bool matches = true;
      for (final entry in selectedOptions.entries) {
        if (variant.combinations[entry.key] != entry.value) {
          matches = false;
          break;
        }
      }
      if (matches && variant.combinations.length == selectedOptions.length) {
        selectedVariant = variant;
        return;
      }
    }
    selectedVariant = null;
  }

  bool _isOptionValueAvailable(String optionName, String optionValue) {
    final variants =
        widget.productDetailFullCardBloc.getVariantsWithCombinations();

    // Create a temporary selection with this option value
    Map<String, String> tempSelection = Map.from(selectedOptions);
    tempSelection[optionName] = optionValue;

    // Check if any variant matches this selection (partially or fully)
    for (final variant in variants) {
      bool matches = true;
      for (final entry in tempSelection.entries) {
        if (variant.combinations[entry.key] != entry.value) {
          matches = false;
          break;
        }
      }
      if (matches) {
        return true;
      }
    }

    return false;
  }

  // Check if an option value is available given the current selection
  bool _isOptionValueAvailableWithCurrentSelection(
      String optionName, String optionValue) {
    // If this is the currently selected option, it's always available
    if (selectedOptions[optionName] == optionValue) {
      return true;
    }

    // Create a temporary selection with this option value
    Map<String, String> tempSelection = Map.from(selectedOptions);
    tempSelection[optionName] = optionValue;

    // Get all variants
    final variants =
        widget.productDetailFullCardBloc.getVariantsWithCombinations();

    // For each other option that's already selected, check if it's compatible with this selection
    for (final entry in selectedOptions.entries) {
      if (entry.key == optionName) continue; // Skip the current option

      bool hasMatchingVariant = variants.any((variant) {
        // Check if this variant matches all currently selected options plus the new one
        bool matches = true;
        for (var selection in tempSelection.entries) {
          if (variant.combinations[selection.key] != selection.value) {
            matches = false;
            break;
          }
        }
        return matches;
      });

      if (!hasMatchingVariant) {
        return false;
      }
    }

    return true;
  }

  int _getSelectedVariantSellingPrice() {
    return selectedVariant?.sellingPrice ??
        widget.productDetailFullCardBloc.getDisplaySellingPrice();
  }

  int _getSelectedVariantMrpPrice() {
    return selectedVariant?.mrpPrice ??
        widget.productDetailFullCardBloc.getDisplayMrpPrice();
  }

  int _getSelectedVariantStock() {
    return selectedVariant?.stock ??
        widget.productDetailFullCardBloc.getDisplayStock();
  }

  int _getDiscountPercentage() {
    final mrp = _getSelectedVariantMrpPrice();
    final selling = _getSelectedVariantSellingPrice();

    if (mrp <= 0 || selling < 0) {
      return 0;
    }

    return (((mrp - selling) / mrp) * 100).round();
  }

  /// Builds the options selection section
  Widget _buildOptionsSection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: _buildOptionSelectors(),
        ),
      ),
    );
  }

  /// Builds a scrollable list of all variants
  Widget _buildVariantListSection() {
    final variants =
        widget.productDetailFullCardBloc.getVariantsWithCombinations();

    if (variants.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Text("No variants found"),
      );
    }

    // Calculate dynamic max height: 50% of screen, allows content to grow but not overflow
    final maxHeight = MediaQuery.of(context).size.height * 0.5;

    return ConstrainedBox(
      constraints: BoxConstraints(maxHeight: maxHeight),
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        shrinkWrap: true,
        itemCount: variants.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final variant = variants[index];
          final isSelected = selectedVariant == variant;
          return GestureDetector(
            onTap: () {
              setState(() {
                selectedVariant = variant;
                selectedOptions = Map.from(variant.combinations);
              });
            },
            child: _buildVariantListItem(variant, isSelected),
          );
        },
      ),
    );
  }

  /// Single variant tile used in the variant list
  Widget _buildVariantListItem(ProductVariant variant, bool isSelected) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill2.withOpacity(0.6),
        borderRadius: BorderRadius.circular(8),
        border:
            isSelected ? Border.all(color: AppColors.appBlack, width: 2) : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 4,
            runSpacing: 2,
            children: variant.combinations.entries.map((entry) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${entry.key}: ",
                    style: AppTextStyle.smallTextRegular(
                      textColor: AppColors.writingBlack1,
                    ),
                  ),
                  Text(
                    entry.value,
                    style: AppTextStyle.smallText(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
          const SizedBox(height: 4),
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                "₹${variant.sellingPrice}",
                style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
              ),
              const SizedBox(width: 8),
              if (variant.mrpPrice != variant.sellingPrice)
                Text(
                  "₹${variant.mrpPrice}",
                  style: AppTextStyle.access0(
                    textColor: AppColors.writingBlack1,
                    isLineThrough: true,
                  ),
                ),
              const SizedBox(width: 8),
              if (variant.mrpPrice != variant.sellingPrice)
                Text(
                  "${variant.discountPercentage.toStringAsFixed(0)}% OFF",
                  style: AppTextStyle.access0(textColor: AppColors.brandGreen),
                ),
              const Spacer(),
              Text(
                variant.stock > 10
                    ? "${variant.stock} units available"
                    : variant.stock > 0
                        ? "Only ${variant.stock} available"
                        : "Out of stock",
                style: AppTextStyle.smallText(
                  textColor: variant.stock > 10
                      ? AppColors.appBlack
                      : variant.stock > 0
                          ? AppColors.orange
                          : AppColors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  bool _canSelectVariant() {
    // Allow selection even if not all options are selected
    // This enables the "Done" button to be pressed to clear selections
    return true;
  }
}
