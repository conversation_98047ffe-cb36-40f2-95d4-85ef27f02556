import 'dart:async';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/features/post/single_post_view/single_post_comment_pagination.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum SubCommentState {
  Initial,
  Loading,
  Success,
  Failed,
  Empty,
  PaginationLoading,
  ViewReplies
}

class SubCommentBloc {
  //region Common variable
  late BuildContext context;
  final PostDetail recentParentCommentDetail;
  List<PostDetail> subCommentList = [];
  int offset = 0;
  int limit = 3;
  final SinglePostViewBloc singlePostViewBloc;

  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final subCommentStateController =
      StreamController<SubCommentState>.broadcast();
  final viewMoreStateController = StreamController<SubCommentState>.broadcast();
//endregion
  //region Constructor
  SubCommentBloc(
      this.context, this.recentParentCommentDetail, this.singlePostViewBloc);

  //endregion
//region Init
  init() {}

//endregion

  //region On tap drawer
  void onTapDrawer({required PostDetail postDetail}) async {
    List<Map<String, dynamic>> accessOptions = [];
    if (postDetail.createdBy!.userOrStoreReference ==
        (AppConstants.appData.isUserView!
            ? AppConstants.appData.userReference!
            : AppConstants.appData.storeReference!)) {
      accessOptions = [
        //Copy
        {
          'title': AppStrings.copyLink,
          'onTap': () {
            Navigator.pop(context);
            CommonMethods.copyText(
                context,
                AppLinkCreateService().createPostLink(
                    postReference: postDetail.postOrCommentReference!));
          },
        },
        //Edit
        {
          'title': AppStrings.edit,
          'onTap': () {
            Navigator.pop(context);
            goToEditPost(postDetail: postDetail);
          },
        },
        //Delete post
        {
          'title': AppStrings.delete,
          'onTap': () {
            Navigator.pop(context);
            confirmDelete(postDetail: postDetail);
          },
        },
        // Add more options if needed
      ];
    } else {
      accessOptions = [
        {
          'title': AppStrings.report,
          'onTap': () {
            Navigator.pop(context);
            // Navigator.pop(context);
            var screen = ReportScreen(
              reference: postDetail.postOrCommentReference!,
              isPostComment: true,
            );
            var route = MaterialPageRoute(builder: (context) => screen);
            Navigator.push(context, route);

//endregion
          },
        },
      ];
    }

    CommonMethods.accessBottomSheet(
      screen: ShareAccessBottomSheet(accessOptions: accessOptions),
      context: context,
    );
  }
  //endregion

  //region Get comment list
  Future<void> getCommentList({required bool isPaginationLoading}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      //If pagination loading
      if (isPaginationLoading) {
        viewMoreStateController.sink.add(SubCommentState.PaginationLoading);
      } else {
        subCommentStateController.sink.add(SubCommentState.Loading);
      }

      //Api call
      var data = await PostService().getCommentList(
          offset: offset,
          limit: limit,
          parentReference: recentParentCommentDetail.postOrCommentReference!);
      //if empty
      if (data.isEmpty) {
        return viewMoreStateController.sink.add(SubCommentState.Empty);
      }
      //Add incoming sub-comments list into subCommentList
      subCommentList.addAll(data);
      // Remove comments in newly added list
      postDataModel.recentlyAddedPostDetail.removeWhere((element) {
        return data.any(
            (e) => element.postOrCommentReference == e.postOrCommentReference);
      });
      //Remove deleted comments
      subCommentList.removeWhere((element) => element.isDeleted!);
      //Add data in post data model
      postDataModel.addPostIntoList(postList: subCommentList);
      //If Current post reference is not in the list then return Empty
      // if(postDataModel.allPostDetailList.where((element) => element.postOrCommentReference == postReference).isEmpty){
      //   return commentStateCtrl.sink.add(CommentsState.Empty);
      // }
      //Success
      subCommentStateController.sink.add(SubCommentState.Success);
      //Make view more to success
      viewMoreStateController.sink.add(SubCommentState.Success);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Failed
      subCommentStateController.sink.add(SubCommentState.Failed);
      return;
    } catch (error) {
      //Failed
      subCommentStateController.sink.add(SubCommentState.Failed);
      return;
    }
  }
//endregion

  //region On tap user or store icon
  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference &&
        AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference &&
        AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Convert date
  String convertDateFormat({required String inputDateTimeString}) {
    DateTime dateTime = DateTime.parse(inputDateTimeString).toLocal();

    // Formatting the date and time
    String formattedDateTime = DateFormat('h:mma MMM d,yyyy').format(dateTime);

    return formattedDateTime;
  }

  //endregion

  //region On tap image
  void onTapImage({required List<String> imageList, required int index}) {
    Widget screen = BuyerImagePreviewScreen(
      productImage: imageList,
      imageIndex: index,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }

  //endregion

  //region Delete post api call
  Future<void> deletePostAndComment({required PostDetail subComment}) async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //Delete status
    bool isSuccess = false;
    //region Try
    try {
      //Api call
      //If post
      if (subComment.postOrCommentReference!.startsWith("P")) {
        isSuccess = await PostService()
            .deletePost(postRefrence: subComment.postOrCommentReference!);
      } else {
        isSuccess = await PostService()
            .deleteComment(postRefrence: subComment.postOrCommentReference!);
      }
      //If is success is false then return
      if (!isSuccess) {
        context.mounted
            ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
                commonErrorMessage: true)
            : null;
        return;
      }
      //Remove local data
      //Remove post detail from the userOrStoreFeeds
      // postDataModel.userOrStoreFeedsList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Remove post detail from the allPostDetailList
      postDataModel.allPostDetailList.removeWhere((element) =>
          element.postOrCommentReference == subComment.postOrCommentReference);

      //Reduce the parent comment count
      recentParentCommentDetail.commentCount =
          recentParentCommentDetail.commentCount! - 1;
      //Refresh ui
      postDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

  //region On tap heart
  Future<void> onTapHeart({required PostDetail postDetail}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      //Update liked count and is liked
      //Toggle the like status
      postDetail.likeStatus = !postDetail.likeStatus!;
      if (postDetail.likeStatus!) {
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      } else {
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      //Refresh ui
      postDataModel.updateUi();
      //Api call
      await PostService().likePost(
          postReference: postDetail.postOrCommentReference!,
          likeStatus: postDetail.likeStatus!);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      return;
    }
  }
//endregion

  //region Go to edit post
  void goToEditPost({required PostDetail postDetail}) {
    Widget screen = EditPostScreen(
      postDetail: postDetail,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route)
        .then((value) {
      // singlePostStateCtrl.sink.add(SinglePostViewState.Success);
      // getSinglePostOrComment();
    });
  }
  //endregion

  //region Go to single post view
  void goToSinglePostView({required String postReference}) {
    var screen = SinglePostViewScreen(
      postReference: postReference,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Confirm delete
  Future confirmDelete({required PostDetail postDetail}) {
    return CommonMethods.appDialogBox(
        context: context,
        widget: OkayAndCancelDialogScreen(
          onTapSecondButton: () {
            deletePostAndComment(subComment: postDetail);
          },
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.areYouSureWantsToDelete,
          firstButtonName: "Cancel",
          secondButtonName: "Delete",
        ));
  }
//endregion

  //region On Tap Share
  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        entityType: EntityType.POST,
        url: AppLinkCreateService().createPostLink(
          postReference: postDetail.postOrCommentReference!,
        ),
        imageLink: postDetail.images!.isEmpty
            ? null
            : postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.name,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,
        message: "Check out this post.",
      ),
      context: context,
    );
    //
    // showModalBottomSheet(
    //     context: context,
    //     isScrollControlled: true,
    //     enableDrag: true,
    //     backgroundColor: AppColors.appWhite,
    //     shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(20), topLeft: Radius.circular(20))),
    //     builder: (context) {
    //       return SingleChildScrollView(
    //           padding: EdgeInsets.zero,
    //           child: Container(
    //             child: Column(
    //               children: [
    //                 Container(
    //                   margin: const EdgeInsets.symmetric(vertical: 20),
    //                   child: Column(
    //                     children: [
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                       verticalSizedBox(10),
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                     ],
    //                   ),
    //                 ),
    //                 BuyerProductShareScreen(url: link, imageLink:imageUrl,),
    //               ],
    //             ),
    //           ));
    //     }).then((value) {
    //   // if (value == null) return;
    //   // supportFilterModel = value;
    //   // applyFilter();
    // });
  }
  //endregion

//region Dispose
  void dispose() {
    subCommentStateController.close();
  }
//endregion
}
