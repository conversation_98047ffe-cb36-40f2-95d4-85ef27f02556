import 'dart:async';

import 'package:flutter/cupertino.dart';

class SearchBloc {
  //region Common variable
  late BuildContext context;
  final List<String> dataList;

  List<String> searchedCategoryList = [];
  final bool? shortData;

  //endregion

//region Text Editing Controller
  final TextEditingController searchTextCtrl = TextEditingController();

//endregion

//region Controller
  final refreshCtrl = StreamController<bool>.broadcast();

//endregion
  //region Constructor
  SearchBloc(
    this.context,
    this.dataList,
    this.shortData,
  );
  //endregion
//region Init
  void init() {
    //Add all category into searched category
    searchedCategoryList.clear();
    searchedCategoryList.addAll(dataList);

    //Short is it is true
    shortData! ? searchedCategoryList.sort() : null;
  }
//endregion

//region On tap back button
  void onTapBack() {
    Navigator.pop(context);
  }
//endregion

//region On tap category
  void onTapCategory({required String value}) {
    // Close keyboard before navigating back
    FocusManager.instance.primaryFocus?.unfocus();
    //Add category to selected
    Navigator.pop(context, value);
  }
//endregion

//region On search
  void onSearch() {
    //Clear searched list
    searchedCategoryList.clear();
    if (searchTextCtrl.text.isEmpty) {
      searchedCategoryList.addAll(dataList);
      //Refresh
      refreshCtrl.sink.add(true);
      return;
    }
    // for(var data in sellerOnBoardingBloc.businessCategoryResponse.businessCategoryList!){
    //   if(data.categoryName!.toLowerCase() == searchTextCtrl.text.toLowerCase()){
    //     searchedCategoryList.add(data);
    //   }
    //   //Refresh
    //   refreshCtrl.sink.add(true);
    // }

    searchedCategoryList = dataList.where((element) {
      return element
          .toLowerCase()
          .contains(searchTextCtrl.text.toLowerCase().replaceAll(" ", ""));
    }).toList();
    //Refresh
    refreshCtrl.sink.add(true);
  }
//endregion

  //region On tap add
  void onTapAdd({required String value}) {
    // Close keyboard before navigating back
    FocusManager.instance.primaryFocus?.unfocus();
    Navigator.pop(context, value);
  }
  //endregion

//region Dispose
  void dispose() {
    refreshCtrl.close();
  }
//endregion
}
