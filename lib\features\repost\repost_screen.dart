import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/post/add_post/add_post_bloc.dart';
import 'package:swadesic/features/repost/repost_bloc.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class RepostScreen extends StatefulWidget {
  const RepostScreen({super.key});

  @override
  State<RepostScreen> createState() => _RepostScreenState();
}

class _RepostScreenState extends State<RepostScreen> {
  //Bloc
  late RepostBloc repostBloc;

  //region Init
  @override
  void initState() {
    repostBloc = RepostBloc(context);
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    repostBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        body: SafeArea(child: body()),
      ),
    );
  }

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      onTapLeading: () {
        Navigator.pop(context);
      },
      context: context,
      isCustomTitle: false,
      title: AppStrings.createPost,
      isMembershipVisible: false,
      isCustomMenuVisible: true,
      isDefaultMenuVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  //region Body
  Widget body() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        await repostBloc.init();
      },
      child: ListView(
        physics: const AlwaysScrollableScrollPhysics(
            parent: BouncingScrollPhysics()),
        children: [
          Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height - 200,
            child: const NoResult(message: AppStrings.noRepostYet),
          ),
        ],
      ),
      // child: Text(AppStrings.noRepostYet,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),) ,
    );
  }

//endregion
//NoResult(message: AppStrings.noPostFound)
}
