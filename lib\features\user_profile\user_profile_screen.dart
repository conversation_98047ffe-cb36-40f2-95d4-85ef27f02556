import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/services/app_link_services/page_url_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:readmore/readmore.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_common_widget.dart';
import 'package:swadesic/features/common_buyer_seller_screen/invalid/invalid_screen.dart';
import 'package:swadesic/features/post/post_screen.dart';
import 'package:swadesic/features/repost/repost_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_bloc.dart';
import 'package:swadesic/features/user_profile/user_profile_common_widgets.dart';
import 'package:swadesic/features/widgets/app_divider/app_devider.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/app_analytics/app_analytics.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';
import 'package:swadesic/features/communication/welcome_store_screen.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access_bloc.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/support_score_card_overlay.dart';

class UserProfileScreen extends StatefulWidget {
  final String userReference;
  final bool fromBottomNavigation;

  const UserProfileScreen(
      {Key? key,
      required this.userReference,
      this.fromBottomNavigation = false})
      : super(key: key);

  @override
  _UserProfileScreenState createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen>
    with
        SingleTickerProviderStateMixin,
        AutoHideNavigationMixin<UserProfileScreen> {
  //Tab controller
  late TabController tabController = TabController(
      length: (widget.userReference == AppConstants.appData.userReference &&
              (AppConstants.appData.isUserView ?? false))
          ? 4
          : 2,
      vsync: this,
      initialIndex: 0);

  //region Bloc
  late UserProfileBloc userProfileBloc;

  //endregion
  //region Init
  @override
  void initState() {
    userProfileBloc =
        UserProfileBloc(context, widget.userReference, tabController);
    userProfileBloc.init();

    // Initialize auto-hide navigation only if not from bottom navigation
    // (to avoid conflicts with main navigation)
    if (!widget.fromBottomNavigation) {
      enableAutoHideNavigation();
      attachScrollControllerToAutoHide(userProfileBloc.scrollController);
    }

    //Add store info in analytics
    AppAnalytics.viewEventLog(reference: widget.userReference);
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    // Detach scroll controller from auto-hide service if it was attached
    if (!widget.fromBottomNavigation) {
      detachScrollControllerFromAutoHide(userProfileBloc.scrollController);
      disableAutoHideNavigation();
    }

    super.dispose();
    tabController.dispose();
    userProfileBloc.dispose();
  }
  //endregion

  //region Navigate to Support Score Card
  void _navigateToSupportScoreCard() {
    // Get the appropriate user info response based on whether viewing own profile or other user's profile
    final userInfoResponse = ((AppConstants.appData.isUserView ?? false) &&
            AppConstants.appData.userReference == widget.userReference)
        ? userProfileBloc.loggedInUserInfoResponse
        : userProfileBloc.otherUserInfoResponse;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return SupportScoreCardOverlay(
          userReference: widget.userReference,
          userDetail: userInfoResponse.userDetail,
        );
      },
    );
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    //if Static user and view from bottom navigation root screen
    // if(widget.fromBottomNavigation && CommonMethods().isStaticUser()){
    //   return Center(
    //     child: Column(
    //       mainAxisSize: MainAxisSize.min,
    //       mainAxisAlignment: MainAxisAlignment.center,
    //       crossAxisAlignment: CrossAxisAlignment.center,
    //
    //       children: [
    //         Row(
    //           mainAxisSize: MainAxisSize.min,
    //           mainAxisAlignment: MainAxisAlignment.center,
    //           crossAxisAlignment: CrossAxisAlignment.center,
    //           children: [
    //             CupertinoButton(
    //               padding: EdgeInsets.zero,
    //               onPressed: () {
    //                 CommonMethods().goToSignUpFlow();
    //               },
    //               child: Container(
    //                 alignment: Alignment.center,
    //                 padding: const EdgeInsets.symmetric(
    //                   horizontal: 20,
    //                   vertical: 10,
    //                 ),
    //                 decoration: const BoxDecoration(color: AppColors.brandGreen, borderRadius: BorderRadius.all(Radius.circular(100))),
    //                 child: Text(
    //                   "Sign in to view your profile",
    //                   style: AppTextStyle.access0(textColor: AppColors.appWhite),
    //                 ),
    //               ),
    //             ),
    //           ],
    //         ),
    //       ],
    //     ),
    //   );
    // }

    return DefaultTabController(
      length: (widget.userReference == AppConstants.appData.userReference &&
              AppConstants.appData.isUserView!)
          ? 4
          : 2,
      child: GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
        },
        child: Scaffold(
            appBar: appBar(),
            backgroundColor: AppColors.appWhite,
            body: SafeArea(
              child: Container(
                  alignment: Alignment.topCenter,
                  // height: MediaQuery.of(context).size.height,
                  child: body()),
              // child: demoBody()),
            )),
      ),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: true,
        onTapLeading: () {
          widget.fromBottomNavigation
              ? userProfileBloc.goToFirstBottomNavigation()
              : Navigator.pop(context);
        },
        customTitleWidget: StreamBuilder<UserProfileState>(
            stream: userProfileBloc.userProfileCtrl.stream,
            builder: (context, snapshot) {
              if (snapshot.data == UserProfileState.Success) {
                return Padding(
                  padding: EdgeInsets.only(
                      left: widget.fromBottomNavigation ? 20 : 0.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AppCommonWidgets.appBarTitleText(
                          text: ((AppConstants.appData.isUserView ?? false) &&
                                  AppConstants.appData.userReference ==
                                      widget.userReference)
                              ? userProfileBloc.loggedInUserInfoResponse
                                  .userDetail!.userName!
                              : userProfileBloc
                                  .otherUserInfoResponse.userDetail!.userName!),
                      Visibility(
                        visible: ((AppConstants.appData.isUserView ?? false) &&
                                AppConstants.appData.userReference ==
                                    widget.userReference)
                            ? userProfileBloc.loggedInUserInfoResponse
                                    .userDetail!.subscriptionType! ==
                                SubscriptionTypeEnum.PREMIUM.name
                            : userProfileBloc.otherUserInfoResponse.userDetail!
                                    .subscriptionType! ==
                                SubscriptionTypeEnum.PREMIUM.name,

                        child: VerifiedBadge(
                          margin: const EdgeInsets.only(left: 5),
                          height: 18,
                          width: 18,
                          subscriptionType: SubscriptionTypeEnum.PREMIUM.name,
                        ),
                        // child: Container(
                        //     padding: const EdgeInsets.symmetric(horizontal: 10),
                        //     child: SvgPicture.asset(AppImages.verified,height: 23,width: 25,)),
                      )
                    ],
                  ),
                );
              }
              return AppCommonWidgets.appBarTitleText(text: "");
            }),
        // titleWidget: Text(UserProfileBloc.getUserDetailsResponse.userDetail!.userName!??"", style: TextStyle(fontFamily: AppConstants.rRegular, fontSize: 19, fontWeight: FontWeight.w700, color: AppColors.appBlack)),
        isDefaultMenuVisible: false,
        isDropdownVisible: widget.fromBottomNavigation,
        isCartVisible: false,
        isLeadingVisible: widget.fromBottomNavigation ? false : true,
        onTapDropdown: () async {
          userProfileBloc.goToSellerAccountScreen();
        },
        customMenuButton:
            widget.userReference != AppConstants.appData.userReference
                ? myMenuButton()
                : const SizedBox(),
        isCustomMenuVisible: !widget.fromBottomNavigation,
        isMembershipVisible: false);
  }

  //endregion

  //region Menu button
  Widget myMenuButton() {
    return PopupMenuButton(
      // shadowColor: Colors.transparent,
      // add icon, by default "3 dot" icon
      // icon: Icon(Icons.book)
      padding: EdgeInsets.zero,
      icon: SvgPicture.asset(AppImages.drawerIcon),
      itemBuilder: (context) {
        return [
          ///Report
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () async {
              userProfileBloc.goToReportScreen();
            },
            padding: EdgeInsets.zero,
            child: SizedBox(
              width: 150,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                      padding: const EdgeInsets.all(10),
                      child:
                          AppCommonWidgets.menuText(text: AppStrings.report)),
                  divider()
                ],
              ),
            ),
          ),

          ///Share
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () {
              userProfileBloc.onTapShareUserProfile(
                  userName: ((AppConstants.appData.isUserView ?? false) &&
                          AppConstants.appData.userReference ==
                              widget.userReference)
                      ? userProfileBloc
                          .loggedInUserInfoResponse.userDetail!.userName!
                      : userProfileBloc
                          .otherUserInfoResponse.userDetail!.userName!);
            },
            padding: EdgeInsets.zero,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                    padding: const EdgeInsets.all(10),
                    child:
                        AppCommonWidgets.menuText(text: AppStrings.shareProfile)

                    // child: appText(AppStrings.shareProfile,
                    //     fontFamily: AppConstants.rRegular,
                    //     fontSize: 14,
                    //     fontWeight: FontWeight.w700
                    // )

                    ),
                // divider()
              ],
            ),
          ),
        ];
      },
    );
  }

  //endregion

  //region Body
  Widget body() {
    return StreamBuilder<UserProfileState>(
        stream: userProfileBloc.userProfileCtrl.stream,
        initialData: UserProfileState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == UserProfileState.Success) {
            return demoBody();
            // return RefreshIndicator(
            //   color: AppColors.brandGreen,
            //   onRefresh: () async {
            //     await userProfileBloc.init();
            //   },
            //   child: ListView(
            //     padding: const EdgeInsets.symmetric(horizontal: 10),
            //     children: [
            //       userProfile(userInfoResponse: (AppConstants.appData.isUserView! && AppConstants.appData.userReference == widget.userReference )?userProfileBloc.loggedInUserInfoResponse:userProfileBloc.otherUserInfoResponse),
            //       verticalSizedBox(15),
            //       followAboutEditCreateSetting(userInfoResponse: (AppConstants.appData.isUserView! && AppConstants.appData.userReference == widget.userReference )?userProfileBloc.loggedInUserInfoResponse:userProfileBloc.otherUserInfoResponse),
            //       verticalSizedBox(20),
            //       ///Un-comment
            //       // findFriend(),
            //       // verticalSizedBox(10),
            //       optionsAndReview(),
            //     ],
            //   ),
            // );
          }
          if (snapshot.data == UserProfileState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          if (snapshot.data == UserProfileState.Failed) {
            Center(
                child: AppCommonWidgets.errorWidget(
                    errorMessage: AppStrings.unableToLoadUserInfo,
                    onTap: () {
                      userProfileBloc.init();
                    }));
            // return const InvalidScreen();
          }
          return Center(
              child: AppCommonWidgets.errorWidget(
                  errorMessage: AppStrings.unableToLoadUserInfo,
                  onTap: () {
                    userProfileBloc.init();
                  }));
        });
  }

  //endregion

  //region Use profile and image
  Widget userProfile({required GetUserDetailsResponse userInfoResponse}) {
    final data = userInfoResponse.userDetail!;
    PageUrlService.setPageUrlAfterBuild('/${data.userName}', 'User Profile');
    return Row(
      children: [
        // Profile Icon
        GestureDetector(
          onTap: () {
            CommonMethods.goToShareStoreAndProfile(
              context: context,
              userDetail: data,
            );
          },
          child: CustomImageContainer(
            height: 80,
            width: 80,
            imageUrl: data.icon,
            imageType: CustomImageContainerType.user,
            showLevelBadge: true,
            level: data.userLevel.toString(),
            badgeWidth: 25,
            badgeHeight: 25,
            userBadgeBorderWidth: 1.05,
            userBadgeFontSize: 15,
            onTapBadge: () {
              _navigateToSupportScoreCard();
            },
          ),
          // child: ClipRRect(
          //     borderRadius: BorderRadius.circular(500),
          //     child: data.icon == null
          //         ? ClipRRect(
          //             borderRadius: BorderRadius.circular(50),
          //             child: SvgPicture.asset(
          //               AppImages.userPlaceHolder,
          //               height: 80,
          //               width: 80,
          //             ))
          //         : SizedBox(
          //             height: 80,
          //             width: 80,
          //             child: extendedImage(data.icon!, customPlaceHolder: AppImages.userPlaceHolder, context, 262, 262, fit: BoxFit.cover),
          //           )),
        ),

        horizontalSizedBox(10),
        //User info
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //Name
              Flexible(
                child: Row(
                  children: [
                    Text("${data.displayName ?? ""}",
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyle.usernameHeading(
                                textColor: AppColors.writingBlack0)
                            .copyWith(height: 0)),
                    Visibility(
                      visible: data.subscriptionType ==
                          SubscriptionTypeEnum.PREMIUM.name,
                      child: VerifiedBadge(
                        margin: const EdgeInsets.only(left: 5),
                        height: 18,
                        width: 18,
                        subscriptionType: SubscriptionTypeEnum.PREMIUM.name,
                      ),
                      // child: Container(
                      //     padding: const EdgeInsets.symmetric(horizontal: 10),
                      //     child: SvgPicture.asset(AppImages.verified,height: 25,width: 25,)),
                    )
                  ],
                ),
              ),
              verticalSizedBox(2),

              ///Todo un-comment
              //Silver support and level
              // Row(
              //   mainAxisSize: MainAxisSize.min,
              //   mainAxisAlignment: MainAxisAlignment.center,
              //   crossAxisAlignment: CrossAxisAlignment.center,
              //   children: const [
              //     Text(
              //       "Silver supporter |",
              //       style: TextStyle(fontSize: 14, color: AppColors.writingColor2, fontFamily: "LatoSemibold", fontWeight: FontWeight.w600),
              //     ),
              //     Text(
              //       " Level: 50",
              //       style: TextStyle(fontSize: 14, color: AppColors.writingColor3, fontFamily: "LatoSemibold", fontWeight: FontWeight.w600),
              //     ),
              //   ],
              // ),
              //Followers and Purchases
              Row(
                children: [
                  //Followers
                  InkWell(
                    onTap: () {
                      userProfileBloc.goToFollowerAndSupporters();
                    },
                    child: Text(
                      "${userInfoResponse.followersCount} followers  ",
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.writingBlack1),
                    ),
                  ),
                  //Purchases
                  InkWell(
                    child: Text(
                      "${userInfoResponse.purchases} purchases",
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.writingBlack1),
                    ),
                  ),
                ],
              ),
            ],
          ),
        )
      ],
    );
  }

  //endregion
  ///Logged in user
  //region Follow, about, Edit create settings
  Widget followAboutEditCreateSetting(
      {required GetUserDetailsResponse userInfoResponse}) {
    final data = userInfoResponse;
    return StreamBuilder<bool>(
        stream: userProfileBloc.followCountCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              verticalSizedBox(2),
              //About user
              Visibility(
                visible: data.userDetail!.aboutUser != null,
                child: Row(
                  children: [
                    Expanded(
                      // child: Text("{}"),
                      child: (data.userDetail?.aboutUser == null ||
                              data.userDetail!.aboutUser!.trim().isEmpty)
                          ? const SizedBox()
                          : ReadMoreText(
                              data.userDetail!.aboutUser!,
                              trimMode: TrimMode.Line,
                              trimLines: 3,
                              colorClickableText: Colors.pink,
                              style: AppTextStyle.contentText0(
                                  textColor: AppColors.appBlack),
                              lessStyle: AppTextStyle.contentText0(
                                  textColor: AppColors.writingBlack1),
                              moreStyle: AppTextStyle.contentText0(
                                  textColor: AppColors.writingBlack1),
                              trimLength: 5,
                              trimCollapsedText: AppStrings.more,
                              trimExpandedText: " ${AppStrings.less}",
                              textAlign: TextAlign.start,
                              annotations: [
                                // Annotation(
                                //   regExp: RegExp(r'#([a-zA-Z0-9_]+)'),
                                //   spanBuilder: ({required String text, TextStyle? textStyle}) => TextSpan(
                                //     text: text,
                                //     style: textStyle?.copyWith(color: Colors.blue),
                                //   ),
                                // ),
                                //User name or handle
                                Annotation(
                                  regExp: RegExp(
                                      r"@[a-zA-Z0-9_]+(?:'s\s+[a-zA-Z0-9\s]+)?"),
                                  spanBuilder: (
                                          {required String text,
                                          TextStyle? textStyle}) =>
                                      TextSpan(
                                    text: text,
                                    style: textStyle?.copyWith(
                                        color: AppColors.brandGreen),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () {
                                        // Extract the username from the tapped text
                                        final userName = text.substring(1);
                                        OnTapTag(context, userName);
                                        //print(userName); // Print the username
                                      },
                                  ),
                                ),
                                //URL
                                Annotation(
                                  regExp: AppConstants.urlRegex,
                                  spanBuilder: (
                                          {required String text,
                                          TextStyle? textStyle}) =>
                                      TextSpan(
                                    text: text,
                                    style: textStyle?.copyWith(
                                        color: AppColors.brandBlack),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () {
                                        CommonMethods.openAppWebView(
                                            webUrl: text,
                                            context: AppConstants
                                                .globalNavigator
                                                .currentContext!);
                                        //print(text); // Print the URL
                                      },
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ],
                ),
              ),
              //Url
              Visibility(
                visible: data.userDetail!.websiteLink != null,
                child: InkWell(
                  onTap: () {
                    CommonMethods.openAppWebView(
                        webUrl: data.userDetail!.websiteLink!,
                        context: context);
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 5),
                    // height: 30,
                    alignment: Alignment.centerLeft,
                    child: Text(
                      data.userDetail!.websiteLink ?? "",
                      maxLines: 1,
                      overflow: TextOverflow.visible,
                      style:
                          AppTextStyle.access0(textColor: AppColors.brandBlack),
                    ),
                  ),
                ),
              ),
              //Also followed by
              ///Un-comment
              // Container(
              //   height: 25,
              //   alignment: Alignment.centerLeft,
              //   child: Text(
              //     "Also followed by north_star129, viswaksen_y and 26 others ",
              //     maxLines: 3,
              //     overflow: TextOverflow.visible,
              //     style: TextStyle(fontSize: 12, color: AppColors.writingBlack, fontFamily: "LatoBold", fontWeight: FontWeight.w700),
              //   ),
              // ),
              verticalSizedBox(9),
              //Edit follow
              editFollow()
            ],
          );
        });
  }

  //endregion

  //region edit and follow
  Widget editFollow() {
    return ((AppConstants.appData.isUserView ?? false) &&
            AppConstants.appData.userReference == widget.userReference)
        ? editSetting()
        : followMessage();
  }

  //endregion

  //region Edit
  Widget editSetting() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Edit profile button
        Expanded(
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: userProfileBloc.editProfileScreen,
            child: Container(
              height: 40,
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: AppColors.textFieldFill1,
                borderRadius: BorderRadius.circular(100),
              ),
              child: Center(
                child: Text(
                  "Edit profile",
                  textAlign: TextAlign.center,
                  style:
                      AppTextStyle.access0(textColor: AppColors.writingBlack0)
                          .copyWith(height: 1.2),
                ),
              ),
            ),
          ),
        ),

        horizontalSizedBox(8),

        // Settings button
        Expanded(
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: userProfileBloc.goToUserSettings,
            child: Container(
              height: 40,
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: AppColors.textFieldFill1,
                borderRadius: BorderRadius.circular(100),
              ),
              child: Center(
                child: Text(
                  "Settings",
                  textAlign: TextAlign.center,
                  style:
                      AppTextStyle.access0(textColor: AppColors.writingBlack0)
                          .copyWith(height: 1.2),
                ),
              ),
            ),
          ),
        ),

        horizontalSizedBox(8),

        // Create store button
        Expanded(
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () => HomeAccessBloc(context).onTapCreateStore(),
            child: Container(
              height: 40,
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: AppColors.brandBlack,
                borderRadius: BorderRadius.circular(100),
              ),
              child: Center(
                child: Text(
                  "Create store",
                  textAlign: TextAlign.center,
                  style: AppTextStyle.access0(textColor: AppColors.appWhite)
                      .copyWith(height: 1.2),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  //endregion

  //region Option and review
  Widget optionsAndReview() {
    return ((AppConstants.appData.isUserView ?? false) &&
            AppConstants.appData.userReference == widget.userReference)
        ? ListView(
            children: const [
              HomeAccess(showSupportScore: true),
            ],
          )
        // ListView(
        //         children: [
        //           ///Find your customers on swadesic
        //           AppCommonWidgets.settingOption(
        //               horizontalPadding: 10,
        //               prefixIconPadding: 5,
        //               optionText: AppStrings.findYourFriendsOnSwadesic,
        //               prefixIcon: AppImages.findYourFriendsIcon,
        //               onTap: () {
        //                 userProfileBloc.goToFindYourCustomer();
        //               }),
        //
        //           ///Supported stores
        //           AppCommonWidgets.settingOption(
        //               horizontalPadding: 10,
        //               prefixIconPadding: 5,
        //               optionText: AppStrings.supportedStores,
        //               prefixIcon: AppImages.supportStore,
        //               onTap: () {
        //                 userProfileBloc.onTapFavouriteStore();
        //               }),
        //
        //           ///Recently visited stores
        //           AppCommonWidgets.settingOption(
        //               horizontalPadding: 10,
        //               prefixIconPadding: 5,
        //               optionText: AppStrings.recentStore,
        //               prefixIcon: AppImages.recentlyVisitedStore,
        //               onTap: () {
        //                 userProfileBloc.onTapRecentlyVisitedStore();
        //               }),
        //
        //           ///My  order
        //           AppCommonWidgets.settingOption(
        //               horizontalPadding: 10,
        //               prefixIconPadding: 5,
        //               optionText: AppStrings.myOrders,
        //               prefixIcon: AppImages.orders,
        //               onTap: () {
        //                 userProfileBloc.goToMyOrderScreen();
        //               }),
        //
        //           ///Suggest an idea
        //           AppCommonWidgets.settingOption(
        //               horizontalPadding: 10,
        //               prefixIconPadding: 5,
        //               optionText: AppStrings.suggestAnIdea,
        //               prefixIcon: AppImages.supportIdea,
        //               onTap: () {
        //                 userProfileBloc.onTapGoToSupport(isReport: false);
        //               }),
        //
        //           ///Support
        //           AppCommonWidgets.settingOption(
        //               horizontalPadding: 10,
        //               prefixIconPadding: 5,
        //               optionText: AppStrings.support,
        //               prefixIcon: AppImages.support,
        //               onTap: () {
        //                 userProfileBloc.onTapGoToSupport(isReport: true);
        //               }),
        //
        //           ///Switch account
        //           AppCommonWidgets.settingOption(
        //               horizontalPadding: 10,
        //               prefixIconPadding: 5,
        //               optionText: AppStrings.switchAccount,
        //               prefixIcon: AppImages.switchAccount,
        //               onTap: () {
        //                 userProfileBloc.goToSellerAccountScreen();
        //               }),
        //           ///User reword and invitees
        //           AppCommonWidgets.settingOption(
        //               horizontalPadding: 10,
        //               prefixIconPadding: 5,
        //               optionText: AppStrings.userReward,
        //               prefixIcon: AppImages.rewardStar,
        //               onTap: () {
        //                 userProfileBloc.goToUserAndInviteesScreen();
        //               }),
        //         ],
        //       )
        : Padding(
            padding: const EdgeInsets.symmetric(vertical: 100),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  AppStrings.noContentYet,
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.writingBlack0),
                ),
                verticalSizedBox(7),
                Text(
                  AppStrings.soonYouCanSee,
                  style: AppTextStyle.contentText0(
                      textColor: AppColors.writingBlack1),
                )
              ],
            ),
          );
  }

  //endregion

  //region Find your friend
  Widget findFriend() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, 1),
            blurRadius: 5,
            color: AppColors.appBlack.withOpacity(0.2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: Text(
              "Find your friends on Whitelabel to know their product experiences",
              maxLines: 2,
              textAlign: TextAlign.start,
              overflow: TextOverflow.visible,
              style: TextStyle(
                  fontSize: 14,
                  color: AppColors.brandBlack,
                  fontFamily: "LatoSemibold",
                  fontWeight: FontWeight.w600),
            ),
          ),
          horizontalSizedBox(5),
          // SvgPicture.asset(AppImages.fiendFriend)
          SizedBox(
              width: 133,
              height: 51,
              child: ImageFiltered(
                imageFilter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
                child: Image.asset(
                  AppImages.fiendFriend,
                  cacheWidth: 399,
                  cacheHeight: 153,
                ),
              ))
        ],
      ),
    );
  }

  //endregion

//region Supported and Recent store
  Widget supportedRecent() {
    return Row(
      children: [
        Expanded(
            child: UserProfileCommonWidgets.options(
          AppImages.heart,
          AppStrings.supportedStores,
          onTap: () {
            userProfileBloc.onTapFavouriteStore();
          },
        )),
        horizontalSizedBox(10),
        Expanded(
            child: UserProfileCommonWidgets.options(
                AppImages.recentlyVisitedStore, AppStrings.recentStore,
                onTap: () {
          userProfileBloc.onTapRecentlyVisitedStore();
        })),
      ],
    );
  }

//endregion

//region Follow message
  Widget followMessage() {
    // bool isFollowed = false;
    // for(var data in userProfileBloc.otherUserInfoResponse.followers!){
    //   if(data.userReference == (AppConstants.appData.isUserView!?AppConstants.appData.userReference! : AppConstants.appData.storeReference!)){
    //     isFollowed = true;
    //   }
    //   else{
    //     isFollowed = false;
    //   }
    // }
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        //Follow
        Expanded(
          child: followUnFollowButton(),
        ),

        horizontalSizedBox(8),

        //Message
        Expanded(
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              if (CommonMethods().isStaticUser()) {
                CommonMethods().goToSignUpFlow();
                return;
              }
              userProfileBloc.goToMessage();
            },
            child: Container(
              height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: AppColors.shadeColor,
                borderRadius: BorderRadius.circular(100),
              ),
              child: Center(
                child: Text(
                  "Message",
                  textAlign: TextAlign.center,
                  style: AppTextStyle.access0(textColor: AppColors.appBlack)
                      .copyWith(height: 1.2),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

//endregion

  //////////////////

  Widget demoBody() {
    return NestedScrollView(
      controller: userProfileBloc.scrollController,
      // controller: buyerViewStoreBloc.scrollController,
      // physics: const NeverScrollableScrollPhysics(),
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        innerBoxIsScrolled = false;
        return <Widget>[
          ///Store detail
          SliverToBoxAdapter(
              child: RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              userProfileBloc.init();
            },
            child: ListView(
              // physics: const BouncingScrollPhysics(),
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(horizontal: 10),
              children: [
                userProfile(
                    userInfoResponse:
                        ((AppConstants.appData.isUserView ?? false) &&
                                AppConstants.appData.userReference ==
                                    widget.userReference)
                            ? userProfileBloc.loggedInUserInfoResponse
                            : userProfileBloc.otherUserInfoResponse),
                verticalSizedBox(15),
                followAboutEditCreateSetting(
                    userInfoResponse:
                        ((AppConstants.appData.isUserView ?? false) &&
                                AppConstants.appData.userReference ==
                                    widget.userReference)
                            ? userProfileBloc.loggedInUserInfoResponse
                            : userProfileBloc.otherUserInfoResponse),
                // verticalSizedBox(20),
                ///Un-comment
                // findFriend(),
                // verticalSizedBox(10),
              ],
            ),
          )),

          ///Tab bar
          tabs(),

          // divider(),
        ];
      },

      ///Tab view
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: tabView(),
      ),
    );
  }

//region Tabs
  StreamBuilder<bool> tabs() {
    return StreamBuilder<bool>(
        stream: userProfileBloc.refreshTabCtrl.stream,
        builder: (context, snapshot) {
          return SliverAppBar(
            expandedHeight: 0.0,
            automaticallyImplyLeading: false,
            leadingWidth: 0.0,
            titleSpacing: 0,
            floating: false,
            pinned: true,
            backgroundColor: AppColors.appWhite,
            collapsedHeight: kToolbarHeight,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: const EdgeInsets.symmetric(horizontal: 0),
              title: (widget.userReference ==
                          AppConstants.appData.userReference &&
                      (AppConstants.appData.isUserView ?? false))
                  //Logged in user
                  ? TabBar(
                      controller: userProfileBloc.tabController,
                      indicator: UnderlineTabIndicator(
                        borderSide: BorderSide(
                          color: AppColors.appBlack,
                          // width: 0.0,
                        ),
                      ),
                      onTap: (index) {
                        // notificationBloc.tabRefreshCtrl.sink.add(true);
                      },
                      padding: EdgeInsets.zero,
                      // isScrollable: true,
                      tabs: [
                          //Feed
                          SizedBox(
                            height: kToolbarHeight,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  AppImages.userFeedTab,
                                  color:
                                      userProfileBloc.tabController.index == 0
                                          ? AppColors.appBlack
                                          : AppColors.writingBlack1,
                                )
                              ],
                            ),
                          ),
                          //Repost
                          SizedBox(
                            height: kToolbarHeight * 0.5,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  AppImages.repostForTab,
                                  color:
                                      userProfileBloc.tabController.index == 1
                                          ? AppColors.appBlack
                                          : AppColors.writingBlack1,
                                )
                              ],
                            ),
                          ),
                          //Saved
                          SizedBox(
                            height: kToolbarHeight * .4,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  AppImages.savePost,
                                  color:
                                      userProfileBloc.tabController.index == 2
                                          ? AppColors.appBlack
                                          : AppColors.writingBlack1,
                                )
                              ],
                            ),
                          ),
                          //Access
                          SizedBox(
                            height: kToolbarHeight,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  AppImages.loggedInUserAccessTab,
                                  color:
                                      userProfileBloc.tabController.index == 3
                                          ? AppColors.appBlack
                                          : AppColors.writingBlack1,
                                )
                              ],
                            ),
                          ),
                        ])
                  : TabBar(
                      controller: userProfileBloc.tabController,
                      indicator: UnderlineTabIndicator(
                        borderSide: BorderSide(
                          color: AppColors.appBlack,
                          // width: 0.0,
                        ),
                      ),
                      onTap: (index) {
                        // notificationBloc.tabRefreshCtrl.sink.add(true);
                      },
                      padding: EdgeInsets.zero,
                      // isScrollable: true,
                      tabs: [
                          //Feed
                          SizedBox(
                            height: kToolbarHeight,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  AppImages.userFeedTab,
                                  color:
                                      userProfileBloc.tabController.index == 0
                                          ? AppColors.appBlack
                                          : AppColors.writingBlack1,
                                )
                              ],
                            ),
                          ),
                          //Repost
                          SizedBox(
                            height: kToolbarHeight,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  AppImages.repostForTab,
                                  color:
                                      userProfileBloc.tabController.index == 1
                                          ? AppColors.appBlack
                                          : AppColors.writingBlack1,
                                )
                              ],
                            ),
                          )
                        ]),
            ),
          );
        });
  }

//endregion

//region Tab view
  TabBarView tabView() {
    return (widget.userReference == AppConstants.appData.userReference &&
            (AppConstants.appData.isUserView ?? false))
        ?
        //Logged in user
        TabBarView(controller: userProfileBloc.tabController, children: [
            PostScreen(
                storeOrUserReference: widget.userReference,
                previousScrollController: userProfileBloc.scrollController),
            //If logged in user
            PostScreen(
              storeOrUserReference: widget.userReference,
              previousScrollController: userProfileBloc.scrollController,
              isRepost: true,
              key: UniqueKey(),
            ),
            //Saved
            PostScreen(
              storeOrUserReference: widget.userReference,
              previousScrollController: userProfileBloc.scrollController,
              isSaved: true,
            ),
            // Container(),
            optionsAndReview(),
          ])

        //Other user view
        : TabBarView(controller: userProfileBloc.tabController, children: [
            PostScreen(
              storeOrUserReference: widget.userReference,
              previousScrollController: userProfileBloc.scrollController,
            ),
            // Container(),
            PostScreen(
              storeOrUserReference: widget.userReference,
              previousScrollController: userProfileBloc.scrollController,
              isRepost: true,
              key: UniqueKey(),
            ),
          ]);
  }

//endregion

  //region Follow and un follow button
  Widget followUnFollowButton() {
    // return
    //   userProfileBloc.otherUserInfoResponse.followStatus!.toLowerCase() == FollowEnum.FOLLOWING.name.toLowerCase()
    //       ? Expanded(
    //     child: CupertinoButton(
    //       padding: EdgeInsets.zero,
    //       onPressed: () {
    //         userProfileBloc.onTapFollowAndSupport(reference: widget.userReference);
    //       },
    //       child: Container(
    //         alignment: Alignment.center,
    //         padding: const EdgeInsets.symmetric(
    //           horizontal: 10,
    //           vertical: 10,
    //         ),
    //         decoration: const BoxDecoration(color: AppColors.textFieldFill1, borderRadius: BorderRadius.all(Radius.circular(100))),
    //         child: Text(
    //           userProfileBloc.otherUserInfoResponse.followStatus!,
    //           style: AppTextStyle.access0(textColor: AppColors.writingBlack0),
    //         ),
    //       ),
    //     ),
    //   )
    //       : Expanded(
    //     child: CupertinoButton(
    //       padding: EdgeInsets.zero,
    //       onPressed: () {
    //         userProfileBloc.onTapFollowAndSupport(reference: widget.userReference);
    //       },
    //       child: Container(
    //         alignment: Alignment.center,
    //         padding: const EdgeInsets.symmetric(
    //           horizontal: 10,
    //           vertical: 10,
    //         ),
    //         decoration: const BoxDecoration(color: AppColors.brandGreen, borderRadius: BorderRadius.all(Radius.circular(100))),
    //         child: Text(
    //           userProfileBloc.otherUserInfoResponse.followStatus!,
    //           style: AppTextStyle.access0(textColor: AppColors.appWhite),
    //         ),
    //       ),
    //     ),
    //   );
    return StreamBuilder<AppState>(
        stream: userProfileBloc.followUnFollowStateCtrl.stream,
        initialData: AppState.Success,
        builder: (context, snapshot) {
          //Success
          if (snapshot.data == AppState.Success) {
            return userProfileBloc.otherUserInfoResponse.followStatus!
                        .toLowerCase() ==
                    FollowEnum.FOLLOWING.name.toLowerCase()
                ? Expanded(
                    child: CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        userProfileBloc.onTapFollowAndSupport(
                            reference: widget.userReference);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 10,
                        ),
                        decoration: BoxDecoration(
                            color: AppColors.textFieldFill1,
                            borderRadius:
                                BorderRadius.all(Radius.circular(100))),
                        child: Center(
                          child: Text(
                            userProfileBloc.otherUserInfoResponse.followStatus!,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.access0(
                                    textColor: AppColors.writingBlack0)
                                .copyWith(height: 1.2),
                          ),
                        ),
                      ),
                    ),
                  )
                : Expanded(
                    child: CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        userProfileBloc.onTapFollowAndSupport(
                            reference: widget.userReference);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 10,
                        ),
                        decoration: BoxDecoration(
                            color: AppColors.brandBlack,
                            borderRadius:
                                BorderRadius.all(Radius.circular(100))),
                        child: Center(
                          child: Text(
                            userProfileBloc.otherUserInfoResponse.followStatus!,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.access0(
                                    textColor: AppColors.appWhite)
                                .copyWith(height: 1.2),
                          ),
                        ),
                      ),
                    ),
                  );
          }
          //Loading
          if (snapshot.data == AppState.Loading) {
            return Expanded(
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  // userProfileBloc.onTapFollowAndSupport(reference: widget.userReference);
                },
                child: Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                      color: AppColors.textFieldFill1,
                      borderRadius: BorderRadius.all(Radius.circular(100))),
                  child: Center(
                    child: Text(
                      AppStrings.loading,
                      textAlign: TextAlign.center,
                      style: AppTextStyle.access0(
                              textColor: AppColors.writingBlack0)
                          .copyWith(height: 1.2),
                    ),
                  ),
                ),
              ),
            );
            return StoreCommonWidgets.storeButtons(
                textAndIcon: Text(
                  AppStrings.loading,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyle.access0(textColor: AppColors.appBlack),
                ),
                verticalPadding: 10,
                onTapButton: () {});
          }

          return Expanded(
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                // userProfileBloc.onTapFollowAndSupport(reference: widget.userReference);
              },
              child: Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 10,
                ),
                decoration: BoxDecoration(
                    color: AppColors.writingBlack0,
                    borderRadius: BorderRadius.all(Radius.circular(100))),
                child: Icon(Icons.warning, color: AppColors.yellow, size: 20),
              ),
            ),
          );
        });
  }
//endregion
}
