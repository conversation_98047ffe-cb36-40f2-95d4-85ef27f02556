import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/seller/inventory_options/add_option_bottom_sheet.dart';
import 'package:swadesic/features/seller/inventory_options/add_variant_bottom_sheet.dart';
import 'package:swadesic/model/product_option/product_option.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/product_variant_service/product_variant_service.dart';
import 'package:swadesic/services/product_option_service/product_option_service.dart';
import 'package:swadesic/services/edit_product_image/edit_product_image.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';

enum InventoryOptionsState { Loading, Success, Failed, Empty }

class InventoryOptionsBloc {
  BuildContext context;
  final String storeReference;
  final Product product;
  final bool isFromUpdateStock;

  // State variables
  bool hasOptions = false;
  List<ProductOption> productOptions = [];
  List<ProductVariant> productVariants = [];

  // Temporary storage for unsaved changes
  bool tempHasOptions = false;
  List<ProductOption> tempProductOptions = [];
  List<ProductVariant> tempProductVariants = [];

  // Cache for preserving data when toggling between Yes/No
  List<ProductOption> cachedProductOptions = [];
  List<ProductVariant> cachedProductVariants = [];

  // Controllers for no-options scenario
  late TextEditingController stockController;
  late TextEditingController mrpController;
  late TextEditingController sellingPriceController;

  // Services
  late ProductVariantService productVariantService;
  late ProductOptionService productOptionService;
  late EditProductAndImageServices editProductAndImageServices;

  // Controllers
  final refreshCtrl = StreamController<bool>.broadcast();
  final stateCtrl = StreamController<InventoryOptionsState>.broadcast();

  InventoryOptionsBloc(
      this.context, this.storeReference, this.product, this.isFromUpdateStock) {
    productVariantService = ProductVariantService();
    productOptionService = ProductOptionService();
    editProductAndImageServices = EditProductAndImageServices();

    // Initialize controllers
    stockController = TextEditingController();
    mrpController = TextEditingController();
    sellingPriceController = TextEditingController();

    // Add listeners to update UI when text changes
    stockController.addListener(() => refreshCtrl.sink.add(true));
    mrpController.addListener(() => refreshCtrl.sink.add(true));
    sellingPriceController.addListener(() => refreshCtrl.sink.add(true));
  }

  void init() {
    // Initialize with existing product options if available
    if (product.options != null && product.options!.isNotEmpty) {
      hasOptions = true;
      productOptions = product.options!.entries
          .map((entry) => ProductOption(
                optionName: entry.key,
                optionValues: entry.value,
              ))
          .toList();
    }

    // Load cached variants from product if available
    if (product.variants != null && product.variants!.isNotEmpty) {
      // Check if we have variants with combinations (indicating options exist)
      final variantsWithCombinations = product.variants!
          .map((variantJson) => ProductVariant.fromJson(variantJson))
          .where((variant) => variant.combinations.isNotEmpty)
          .toList();

      if (variantsWithCombinations.isNotEmpty) {
        // Product has variants with combinations, so it has options
        hasOptions = true;
        productVariants = variantsWithCombinations;

        // If options weren't already loaded from product.options, extract them from variants
        if (productOptions.isEmpty) {
          final optionsMap = <String, Set<String>>{};
          for (final variant in variantsWithCombinations) {
            variant.combinations.forEach((key, value) {
              optionsMap.putIfAbsent(key, () => <String>{}).add(value);
            });
          }
          productOptions = optionsMap.entries
              .map((entry) => ProductOption(
                    optionName: entry.key,
                    optionValues: entry.value.toList(),
                  ))
              .toList();
        }
      } else {
        // Check if we have a no-variant data (product without options but with variant data)
        final noVariantList = product.variants!
            .map((variantJson) => ProductVariant.fromJson(variantJson))
            .where((variant) => variant.combinations.isEmpty)
            .toList();

        if (noVariantList.isNotEmpty) {
          // Load the no-variant data into controllers
          final noVariantData = noVariantList.first;
          stockController.text = noVariantData.stock.toString();
          mrpController.text = noVariantData.mrpPrice.toString();
          sellingPriceController.text = noVariantData.sellingPrice.toString();
        }
      }
    }

    // Only load from API if this is a real product (not a temporary one from edit form)
    // and we don't already have cached data
    if (product.productReference != null &&
        product.productReference != "New" &&
        product.productReference != "cached_data" &&
        productVariants.isEmpty &&
        !hasOptions) {
      loadProductVariants();
    }

    // Initialize controllers for no-options scenario if not already set
    if (stockController.text.isEmpty) {
      stockController.text = product.inStock?.toString() ?? '';
    }
    if (mrpController.text.isEmpty) {
      mrpController.text = product.mrpPrice?.toString() ?? '';
    }
    if (sellingPriceController.text.isEmpty) {
      sellingPriceController.text = product.sellingPrice?.toString() ?? '';
    }

    // Initialize temp variables with current state
    tempHasOptions = hasOptions;
    tempProductOptions = List.from(productOptions);
    tempProductVariants = List.from(productVariants);

    // Initialize cache with the loaded data to preserve it during toggle
    cachedProductOptions = List.from(productOptions);
    cachedProductVariants = List.from(productVariants);

    refreshCtrl.sink.add(true);
  }

  void setHasOptions(bool value) {
    tempHasOptions = value;
    if (!tempHasOptions) {
      // When switching to "No", update cache with current temp data before clearing
      if (tempProductOptions.isNotEmpty || tempProductVariants.isNotEmpty) {
        cachedProductOptions = List.from(tempProductOptions);
        cachedProductVariants = List.from(tempProductVariants);
      }
      // Clear temp data for "No" option
      tempProductOptions.clear();
      tempProductVariants.clear();
    } else {
      // When switching to "Yes", restore from cache if available
      if (cachedProductOptions.isNotEmpty || cachedProductVariants.isNotEmpty) {
        tempProductOptions = List.from(cachedProductOptions);
        tempProductVariants = List.from(cachedProductVariants);
      }
    }
    refreshCtrl.sink.add(true);
  }

  void showAddOptionBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddOptionBottomSheet(
        onOptionAdded: (option, {String? oldOptionName}) {
          addProductOption(option);
        },
      ),
    );
  }

  void showEditOptionBottomSheet(ProductOption option) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddOptionBottomSheet(
        existingOption: option,
        onOptionAdded: (updatedOption, {String? oldOptionName}) {
          updateProductOption(option, updatedOption,
              oldOptionName: oldOptionName);
        },
        onOptionDeleted: () {
          deleteProductOption(option);
        },
      ),
    );
  }

  void showAddVariantBottomSheet() {
    if (tempProductOptions.isEmpty) {
      CommonMethods.toastMessage(
        AppStrings.pleaseAddProductOptionsFirst,
        context,
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddVariantBottomSheet(
        productOptions: tempProductOptions,
        existingVariants: tempProductVariants,
        onVariantAdded: (variant) {
          addProductVariant(variant);
        },
      ),
    );
  }

  void showEditVariantBottomSheet(ProductVariant variant) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddVariantBottomSheet(
        productOptions: tempProductOptions,
        existingVariants: tempProductVariants,
        existingVariant: variant,
        onVariantAdded: (updatedVariant) {
          updateProductVariant(variant, updatedVariant);
        },
        onVariantDeleted: () {
          deleteProductVariant(variant);
        },
      ),
    );
  }

  void addProductOption(ProductOption option) {
    // Check if option name already exists
    if (tempProductOptions.any((existing) =>
        existing.optionName.toLowerCase() == option.optionName.toLowerCase())) {
      CommonMethods.toastMessage(
        AppStrings.optionWithThisNameAlreadyExists,
        context,
      );
      return;
    }

    tempProductOptions.add(option);
    // Update cache to preserve new data
    cachedProductOptions = List.from(tempProductOptions);
    refreshCtrl.sink.add(true);
  }

  void updateProductOption(ProductOption oldOption, ProductOption newOption,
      {String? oldOptionName}) async {
    final index = tempProductOptions.indexOf(oldOption);
    if (index != -1) {
      // Check if this is an existing product in the database
      final isExistingProduct = product.productReference != null &&
          product.productReference != "New" &&
          product.productReference != "cached_data";

      // If option name has changed and we have an existing product, call the rename API
      if (oldOptionName != null && isExistingProduct) {
        try {
          await productOptionService.renameProductOption(
            productReference: product.productReference!,
            currentName: oldOptionName,
            newName: newOption.optionName,
          );

          // After successful API call, refresh variants from database
          await loadProductVariants();

          // Update temp variants with fresh data
          tempProductVariants = List.from(productVariants);
          cachedProductVariants = List.from(productVariants);

          if (context.mounted) {
            CommonMethods.toastMessage(
              "Option renamed successfully and variants updated",
              context,
            );
          }
        } catch (e) {
          if (context.mounted) {
            CommonMethods.toastMessage(
              "Failed to rename option: ${e.toString()}",
              context,
            );
          }
          return; // Don't update local state if API call failed
        }
      }

      tempProductOptions[index] = newOption;

      // Update variants that use this option locally
      // This handles both new products and existing products (for local changes)
      if (oldOptionName != null) {
        // Option name has changed - update all variant combinations
        for (var variant in tempProductVariants) {
          if (variant.combinations.containsKey(oldOptionName)) {
            final value = variant.combinations[oldOptionName];
            variant.combinations.remove(oldOptionName);
            // Only add the new combination if the value is still valid
            if (newOption.optionValues.contains(value)) {
              variant.combinations[newOption.optionName] = value!;
            }
          }
        }
      } else {
        // Only option values changed - update variants that use removed values
        for (var variant in tempProductVariants) {
          if (variant.combinations.containsKey(oldOption.optionName)) {
            final value = variant.combinations[oldOption.optionName];
            // If the current value is no longer in the new option values, remove the variant combination
            if (!newOption.optionValues.contains(value)) {
              variant.combinations.remove(oldOption.optionName);
            }
          }
        }
      }

      // Update cache to preserve changes
      cachedProductOptions = List.from(tempProductOptions);
      cachedProductVariants = List.from(tempProductVariants);
      refreshCtrl.sink.add(true);
    }
  }

  void deleteProductOption(ProductOption option) {
    tempProductOptions.remove(option);

    // Remove variants that use this option
    tempProductVariants.removeWhere(
        (variant) => variant.combinations.containsKey(option.optionName));

    // Update cache to preserve changes
    cachedProductOptions = List.from(tempProductOptions);
    cachedProductVariants = List.from(tempProductVariants);
    refreshCtrl.sink.add(true);
  }

  void addProductVariant(ProductVariant variant) {
    // Check if variant with same combinations already exists
    if (tempProductVariants.any(
        (existing) => existing.hasSameCombinations(variant.combinations))) {
      CommonMethods.toastMessage(
        AppStrings.thisVariantAlreadyExists,
        context,
      );
      return;
    }

    tempProductVariants.add(variant);
    // Update cache to preserve new data
    cachedProductVariants = List.from(tempProductVariants);
    refreshCtrl.sink.add(true);
  }

  void updateProductVariant(
      ProductVariant oldVariant, ProductVariant newVariant) {
    final index = tempProductVariants.indexOf(oldVariant);
    if (index != -1) {
      tempProductVariants[index] = newVariant;
      // Update cache to preserve changes
      cachedProductVariants = List.from(tempProductVariants);
      refreshCtrl.sink.add(true);
    }
  }

  Future<void> deleteProductVariant(ProductVariant variant) async {
    try {
      // Show confirmation dialog
      final shouldDelete = await showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) => AlertDialog(
              title: Text(AppStrings.deleteVariant),
              content: Text(AppStrings.areYouSureDeleteVariant),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(AppStrings.cancel),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(
                    AppStrings.delete,
                    style: TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ) ??
          false;

      if (!shouldDelete) return;

      // Show loading state
      stateCtrl.add(InventoryOptionsState.Loading);

      // Call API to delete variant if it has a reference (not a new variant)
      if (variant.variantReference != null &&
          variant.variantReference!.isNotEmpty) {
        await productVariantService.deleteVariant(
          variantReference: variant.variantReference!,
        );
      }

      // Update local state
      tempProductVariants.removeWhere((v) => v == variant);
      cachedProductVariants = List.from(tempProductVariants);

      // Update UI
      refreshCtrl.sink.add(true);
      stateCtrl.add(InventoryOptionsState.Success);

      // Show success message
      if (context.mounted) {
        CommonMethods.toastMessage(
          AppStrings.variantDeletedSuccessfully,
          context,
        );
      }
    } catch (e) {
      // Revert UI state on error
      refreshCtrl.sink.add(true);
      stateCtrl.add(InventoryOptionsState.Failed);

      // Show error message
      if (context.mounted) {
        CommonMethods.toastMessage(
          "${AppStrings.failedToDeleteVariant}: ${e.toString()}",
          context,
        );
      }
    }
  }

  Future<void> loadProductVariants() async {
    try {
      stateCtrl.sink.add(InventoryOptionsState.Loading);
      final response = await productVariantService.getProductVariants(
        productReference: product.productReference!,
      );
      productVariants = response.productVariants;
      stateCtrl.sink.add(InventoryOptionsState.Success);
      refreshCtrl.sink.add(true);
    } catch (e) {
      stateCtrl.sink.add(InventoryOptionsState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(
          "${AppStrings.failedToLoadVariants}: ${e.toString()}",
          context,
        );
      }
    }
  }

  Future<bool> _updateStockAndCloseScreen(
      List<ProductVariant> variants, Product product) async {
    try {
      // Create a list of all variant update futures
      final updateFutures = variants
          .map((variant) => productVariantService.createOrUpdateVariant(
                productReference: product.productReference!,
                mrpPrice: variant.mrpPrice,
                sellingPrice: variant.sellingPrice,
                stock: variant.stock,
                combinations: variant.combinations,
              ))
          .toList();

      // Wait for all updates to complete
      await Future.wait(updateFutures);

      // Update the product in ProductDataModel after successful API call
      // Check context validity before proceeding
      if (!context.mounted) return false;

      // Get ProductDataModel reference safely
      final productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);

      // Create a copy of the product to avoid reference issues
      final updatedProduct = Product();
      updatedProduct.copyFrom(product);

      // Update the product with the new data
      if (tempHasOptions && tempProductOptions.isNotEmpty) {
        updatedProduct.options = {};
        for (var option in tempProductOptions) {
          updatedProduct.options![option.optionName] = option.optionValues;
        }
      } else {
        updatedProduct.options = product.options != null
            ? Map<String, List<String>>.from(product.options!)
            : null;
      }

      updatedProduct.variants =
          variants.map((variant) => variant.toJson()).toList();

      // For no-options scenario, update the basic price fields
      // if (!tempHasOptions || tempProductOptions.isEmpty) {
      //   updatedProduct.inStock = variants.first.stock;
      //   updatedProduct.mrpPrice = variants.first.mrpPrice;
      //   updatedProduct.sellingPrice = variants.first.sellingPrice;
      // }

      // Add the updated product to the data model (this will replace the existing one)
      productDataModel.addProductIntoList(products: [updatedProduct]);

      // Show success message using CommonMethods to avoid context issues
      if (context.mounted) {
        CommonMethods.toastMessage('Stock updated successfully', context);
      }

      _triggerStoreProductRefresh(product.storeReference!);

      // Navigation to BuyerViewStoreScreen will be handled by the calling method
    } catch (e) {
      // Show error message using CommonMethods to avoid context issues
      if (context.mounted) {
        CommonMethods.toastMessage(
            'Failed to update stock for some variants', context);
      }
      // Don't close the screen on error
      return false;
    }
    return true;
  }

  // New method to trigger store product refresh
  void _triggerStoreProductRefresh(String storeReference) {
    // Use the existing global stream controller to notify store products to refresh
    try {
      // Send a refresh notification for this specific store
      AppConstants.productRefreshCtrl.sink.add({
        'storeReference': storeReference,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      // Ignore if the controller doesn't exist or there's an error
      debugPrint('Failed to trigger store product refresh: $e');
    }
  }

  Future<void> onTapSave({required bool isFromUpdateStock}) async {
    // Commit temporary changes to actual data
    hasOptions = tempHasOptions;
    productOptions = List.from(tempProductOptions);
    productVariants = List.from(tempProductVariants);

    // Update cache with saved data
    cachedProductOptions = List.from(productOptions);
    cachedProductVariants = List.from(productVariants);

    if (hasOptions && productOptions.isNotEmpty) {
      // Has options scenario
      // Create a copy of the product to avoid reference issues
      final updatedProduct = Product();
      updatedProduct.copyFrom(product);

      updatedProduct.options = {};
      for (var option in productOptions) {
        updatedProduct.options![option.optionName] = option.optionValues;
      }
      // Cache variants in product for persistence
      updatedProduct.variants =
          productVariants.map((variant) => variant.toJson()).toList();

      if (isFromUpdateStock) {
        try {
          // First update the product with the new options
          await editProductAndImageServices.editOnlyProduct(
            productReference: product.productReference!,
            productName: product.productName ?? '',
            brandName: product.brandName ?? '',
            productCategory: product.productCategory ?? '',
            productDescription: product.productDescription ?? '',
            promotionLink: product.promotionLink ?? '',
            inStock: product.inStock ?? 0,
            mrpPrice: product.mrpPrice ?? 0,
            sellingPrice: product.sellingPrice ?? 0,
            storeid: product.storeid!,
            swadeshiMade: product.swadeshiMade ?? '',
            swadeshiOwned: product.swadeshiOwned ?? '',
            swadeshiBrand: product.swadeshiBrand ?? '',
            hashTag: product.hashTag ?? '',
            gender: product.targetGender ?? '',
            isAffiliatePromotionEnabled: product.isPromotionEnabled ?? false,
            affiliateCommissionAmount: product.promotionAmount ?? 0.0,
            productSlug: product.productSlug ?? '',
            productCode: product.productCode ?? '',
            options: updatedProduct.options,
          );

          // Convert JSON variants back to ProductVariant objects
          final variants = updatedProduct.variants!
              .map((json) => ProductVariant.fromJson(json))
              .toList();

          // Then update stock and close screen
          final success =
              await _updateStockAndCloseScreen(variants, updatedProduct);
          if (success && context.mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                  builder: (context) => BuyerViewSingleProductScreen(
                        productReference: updatedProduct.productReference,
                      )),
            );
            return;
          }
        } catch (e) {
          if (context.mounted) {
            CommonMethods.toastMessage(
              "Failed to update product options: ${e.toString()}",
              context,
            );
          }
          // Don't close the screen on error
          return;
        }
      } else {
        // Update the original product object for returning data
        product.options = updatedProduct.options;
        product.variants = updatedProduct.variants;
      }
    } else {
      // No options scenario - create a single variant with empty combinations
      // Create a copy of the product to avoid reference issues
      final updatedProduct = Product();
      updatedProduct.copyFrom(product);
      updatedProduct.options = {};

      // Validate pricing values before saving
      final mrpValue = int.tryParse(mrpController.text) ?? 0;
      final sellingPriceValue = int.tryParse(sellingPriceController.text) ?? 0;
      final stockValue = int.tryParse(stockController.text) ?? 0;

      // Security validation: Don't allow zero or negative prices
      if (mrpValue <= 0) {
        if (context.mounted) {
          CommonMethods.toastMessage(
            "MRP cannot be zero or negative",
            context,
          );
        }
        return;
      }

      if (sellingPriceValue <= 0) {
        if (context.mounted) {
          CommonMethods.toastMessage(
            "Selling price cannot be zero or negative",
            context,
          );
        }
        return;
      }

      // Additional validation: Selling price should not be greater than MRP
      if (sellingPriceValue > mrpValue) {
        if (context.mounted) {
          CommonMethods.toastMessage(
            "Selling price cannot be greater than MRP",
            context,
          );
        }
        return;
      }

      // Update product with validated pricing info
      updatedProduct.inStock = stockValue;
      updatedProduct.mrpPrice = mrpValue;
      updatedProduct.sellingPrice = sellingPriceValue;

      // Create a single variant with empty combinations for API consistency
      final singleVariant = ProductVariant(
        combinations: {}, // Empty combinations for no-options scenario
        mrpPrice: updatedProduct.mrpPrice ?? 0,
        sellingPrice: updatedProduct.sellingPrice ?? 0,
        stock: updatedProduct.inStock ?? 0,
        productReference: updatedProduct.productReference,
      );

      updatedProduct.variants = [singleVariant.toJson()];

      // Update the productVariants list to match what we just set in product.variants
      productVariants = [singleVariant];

      if (isFromUpdateStock) {
        try {
          // First update the product with the new options (empty in this case)
          await editProductAndImageServices.editOnlyProduct(
            productReference: product.productReference!,
            productName: product.productName ?? '',
            brandName: product.brandName ?? '',
            productCategory: product.productCategory ?? '',
            productDescription: product.productDescription ?? '',
            promotionLink: product.promotionLink ?? '',
            inStock: updatedProduct.inStock ?? 0,
            mrpPrice: updatedProduct.mrpPrice ?? 0,
            sellingPrice: updatedProduct.sellingPrice ?? 0,
            storeid: product.storeid!,
            swadeshiMade: product.swadeshiMade ?? '',
            swadeshiOwned: product.swadeshiOwned ?? '',
            swadeshiBrand: product.swadeshiBrand ?? '',
            hashTag: product.hashTag ?? '',
            gender: product.targetGender ?? '',
            isAffiliatePromotionEnabled: product.isPromotionEnabled ?? false,
            affiliateCommissionAmount: product.promotionAmount ?? 0.0,
            productSlug: product.productSlug ?? '',
            productCode: product.productCode ?? '',
            options: updatedProduct.options,
          );

          // Then update stock and close screen
          final success =
              await _updateStockAndCloseScreen([singleVariant], updatedProduct);
          if (success && context.mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                  builder: (context) => BuyerViewSingleProductScreen(
                        productReference: updatedProduct.productReference,
                      )),
            );
            return;
          }
        } catch (e) {
          if (context.mounted) {
            CommonMethods.toastMessage(
              "Failed to update product: ${e.toString()}",
              context,
            );
          }
          // Don't close the screen on error
          return;
        }
      } else {
        // Update the original product object for returning data
        product.inStock = updatedProduct.inStock;
        product.mrpPrice = updatedProduct.mrpPrice;
        product.sellingPrice = updatedProduct.sellingPrice;
        product.options = updatedProduct.options;
        product.variants = updatedProduct.variants;
      }
    }

    // Close screen and return data in the format expected by AddEditProductFieldsBloc
    // Only pop the screen if we're not navigating to BuyerViewStoreScreen
    if (!isFromUpdateStock) {
      final resultData = {
        'options': product.options ?? {},
        'variants': productVariants,
        'hasMultipleOptions': hasOptions && productOptions.isNotEmpty,
      };

      if (context.mounted) {
        Navigator.pop(context, resultData);
      }
    }
  }

  void dispose() {
    refreshCtrl.close();
    stateCtrl.close();
    stockController.dispose();
    mrpController.dispose();
    sellingPriceController.dispose();
  }
}
