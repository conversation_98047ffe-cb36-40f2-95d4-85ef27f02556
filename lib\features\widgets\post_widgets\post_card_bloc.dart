import 'dart:async';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/liked_user_or_stores/liked_user_or_store_screen.dart';
import 'package:swadesic/features/post/post_pagination.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/tagged_items_widget/tagged_items_widget.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/hold_visited_references/hold_visited_references.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/post_tagged_objects_response/post_tagged_objects_response.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/services/add_visited_reference/add_visited_references.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:async/async.dart';

enum PostCardStsate { Loading, Success, Failed, Empty }

class PostCardBloc {
  //region Common variable
  late BuildContext context;
  final PostDetail postDetail;

  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final postCardCtrl = StreamController<PostCardStsate>.broadcast();
//endregion
  //region Constructor
  PostCardBloc(this.context, this.postDetail);

  //endregion
//region Init
  init() async {}

//endregion

  //region On tap drawer
  void onTapDrawer({required PostDetail postDetail}) async {
    List<Map<String, dynamic>> accessOptions = [];
    if (postDetail.createdBy!.userOrStoreReference ==
        (AppConstants.appData.isUserView!
            ? AppConstants.appData.userReference!
            : AppConstants.appData.storeReference!)) {
      accessOptions = [
        //Copy
        {
          'title': AppStrings.copyLink,
          'onTap': () {
            Navigator.pop(context);
            CommonMethods.copyText(
                context,
                AppLinkCreateService().createPostLink(
                    postReference: postDetail.postOrCommentReference!));
          },
        },
        //Share
        {
          'title': AppStrings.share,
          'onTap': () {
            Navigator.pop(context);
            onTapShare(postDetail: postDetail);
          },
        },
        //Edit
        {
          'title': AppStrings.edit,
          'onTap': () {
            Navigator.pop(context);
            goToEditPost(postDetail: postDetail);
          },
        },
        //Delete post
        {
          'title': AppStrings.delete,
          'onTap': () {
            Navigator.pop(context);
            confirmDelete(postDetail: postDetail);
          },
        },
        // Add more options if needed
      ];
    } else {
      accessOptions = [
        //Copy
        {
          'title': AppStrings.copyLink,
          'onTap': () {
            Navigator.pop(context);
            CommonMethods.copyText(
                context,
                AppLinkCreateService().createPostLink(
                    postReference: postDetail.postOrCommentReference!));
          },
        },
        //Share
        {
          'title': AppStrings.share,
          'onTap': () {
            Navigator.pop(context);
            onTapShare(postDetail: postDetail);
          },
        },
        //Report
        {
          'title': AppStrings.report,
          'onTap': () {
            Navigator.pop(context);
            // Navigator.pop(context);
            //If non register user
            if (CommonMethods().isStaticUser()) {
              CommonMethods().goToSignUpFlow();
              return;
            }
            var screen = ReportScreen(
              reference: postDetail.postOrCommentReference!,
              isPostComment: true,
            );
            var route = MaterialPageRoute(builder: (context) => screen);
            Navigator.push(context, route);

//endregion
          },
        },
      ];
    }

    CommonMethods.accessBottomSheet(
      screen: ShareAccessBottomSheet(accessOptions: accessOptions),
      context: context,
    );
  }
  //endregion

  //region Visited
  Future<void> visited() async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      postDetail.isVisited = await PostService()
          .visitedPost(postReference: postDetail.postOrCommentReference!);
      //Refresh ui
      // postDataModel.updateUi();
    } on ApiErrorResponseMessage catch (error) {
      // context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    } catch (error) {
      //Failed
      // context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true):null;
      return;
    }
  }
//endregion

  //region Convert date
  String convertDateFormat({required String inputDateTimeString}) {
    DateTime dateTime = DateTime.parse(inputDateTimeString).toLocal();

    // Formatting the date and time
    String formattedDateTime = DateFormat('h:mma MMM d, yyyy').format(dateTime);

    return formattedDateTime;
  }

  //endregion

  //region On tap image
  void onTapImage({required List<String> imageList, required int index}) {
    Widget screen = BuyerImagePreviewScreen(
      productImage: imageList,
      imageIndex: index,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }

  //endregion

  //region On tap user or store icon
  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference &&
        AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference &&
        AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Delete post and comment
  Future<void> deletePostAndComment({required PostDetail postDetail}) async {
    bool isSuccess = false;
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //region Try
    try {
      //Delete post
      if (postDetail.postOrCommentReference!.startsWith("PO")) {
        isSuccess = await PostService()
            .deletePost(postRefrence: postDetail.postOrCommentReference!);
      }
      //Delete comment
      if (postDetail.postOrCommentReference!.startsWith("CO")) {
        isSuccess = await PostService()
            .deleteComment(postRefrence: postDetail.postOrCommentReference!);
      }
      //If is success is false then return
      if (!isSuccess) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
        return;
      }
      //Remove local data
      //Remove post detail from the userOrStoreFeeds
      // postDataModel.userOrStoreFeedsList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Remove post detail from the allPostDetailList

      postDataModel.deletePostDetail(postDetail: postDetail);

      //Refresh ui
      postDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }

//endregion

  //region View comment
  viewComment({required String productRef}) {
    var screen = SinglePostViewScreen(
      postReference: productRef,
      isFromProductScreen: true,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region On tap heart
  // Future<void>onTapHeart({required PostDetail postDetail})async{
  //   try{
  //     // Get reference to the PostDataModel
  //     var postDataModel = Provider.of<PostDataModel>(context, listen: false);
  //     //Update liked count and is liked
  //     //Toggle the like status
  //     postDetail.likeStatus = !postDetail.likeStatus!;
  //     if(postDetail.likeStatus!){
  //       postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
  //     }
  //     else{
  //       postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
  //     }
  //     //Refresh ui
  //     postDataModel.updateUi();
  //     //Api call
  //      await PostService().likePost(postReference: postDetail.postOrCommentReference!, likeStatus: !postDetail.likeStatus!);
  //
  //   }
  //   on ApiErrorResponseMessage catch(error){
  //     context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
  //     return;
  //   }
  //   catch(error){
  //     //Failed
  //     context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true):null;
  //     return;
  //   }
  // }
//endregion

  //region Go to edit post
  void goToEditPost({required PostDetail postDetail}) {
    Widget screen = EditPostScreen(postDetail: postDetail);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion

  //region On tap heart
  Future<void> onTapHeart({required PostDetail postDetail}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      //Update liked count and is liked
      //Toggle the like status
      postDetail.likeStatus = !postDetail.likeStatus!;
      if (postDetail.likeStatus!) {
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      } else {
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      //Refresh ui
      postDataModel.updateUi();
      //Post like api call
      if (postDetail.postOrCommentReference!.startsWith("PO")) {
        await PostService().likePost(
            postReference: postDetail.postOrCommentReference!,
            likeStatus: postDetail.likeStatus!);
      }
      //Comment like api call
      if (postDetail.postOrCommentReference!.startsWith("CO")) {
        //await PostService().likeCo(postReference: postDetail.postOrCommentReference!, likeStatus: postDetail.likeStatus!);
      }
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      return;
    }
  }
//endregion

  //region Repost
  Future<void> rePost({required PostDetail postDetail}) async {
    final bool isReposted =
        postDetail.contentCategory == EntityType.REPOST.name;

    //Check if user is static
    if (CommonMethods().isStaticUser()) {
      CommonMethods().goToSignUpFlow();
      return;
    }
    if (isReposted) {
      // Show confirmation dialog
      await CommonMethods.appDialogBox(
        context: context,
        widget: OkayAndCancelDialogScreen(
          onTapSecondButton: () {
            _performRepost(postDetail: postDetail, isReposted: isReposted);
          },
          onTapFirstButton: () {
            // Cancel - do nothing, dialog will close automatically
          },
          previousScreenContext: context,
          isMessageVisible: true,
          message: 'Are you sure you want to undo this repost?',
          firstButtonName: "Cancel",
          secondButtonName: "Undo Repost",
        ),
      );
    } else {
      _performRepost(postDetail: postDetail, isReposted: isReposted);
    }
  }
  //endregion

  //region Perform repost
  Future<void> _performRepost(
      {required PostDetail postDetail, required bool isReposted}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      // Update UI immediately for better responsiveness
      var post = postDataModel.allPostDetailList.firstWhere((element) =>
          element.postOrCommentReference == postDetail.postOrCommentReference);

      // Toggle repost status and update count
      bool newRepostStatus = !isReposted;
      post.contentCategory =
          newRepostStatus ? EntityType.REPOST.name : EntityType.POST.name;

      // Update repost count
      if (newRepostStatus) {
        post.repostCount = (post.repostCount ?? 0) + 1;
      } else {
        post.repostCount = (post.repostCount ?? 1) - 1;
      }

      // Update UI immediately
      postDataModel.updateUi();

      // Make API call
      await PostService().repost(
        postReference: postDetail.postOrCommentReference!,
        repostStatus: newRepostStatus,
      );

      if (newRepostStatus) {
        CommonMethods.toastMessage("Reposted", context, toastShowTimer: 5);
      }
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      return;
    }
  }
//endregion

  //region Save
  Future<void> savePost({required PostDetail postDetail}) async {
    try {
      //Check if user is static
      if (CommonMethods().isStaticUser()) {
        CommonMethods().goToSignUpFlow();
        return;
      }
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      //If post has already saved
      if (postDetail.saveStatus!) {
        postDataModel.allPostDetailList
            .firstWhere((element) =>
                element.postOrCommentReference ==
                postDetail.postOrCommentReference)
            .saveStatus = false;
      } else {
        postDataModel.allPostDetailList
            .firstWhere((element) =>
                element.postOrCommentReference ==
                postDetail.postOrCommentReference)
            .saveStatus = true;
        //Reposted toast message
        CommonMethods.toastMessage("Saved", context, toastShowTimer: 5);
      }

      //Api call
      PostService().savePost(
          postReference: postDetail.postOrCommentReference!,
          saveStatus: postDetail.saveStatus!);

      //Refresh ui
      postDataModel.updateUi();
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      return;
    }
  }
//endregion

//
//   //region On tap drawer
//   void onTapDrawer({required PostDetail postDetail})async{
//     List<Map<String, dynamic>> accessOptions = [];
//     if(postDetail.createdBy!.userOrStoreReference ==
//         (AppConstants.appData.isUserView!
//             ?AppConstants.appData.userReference!
//             :AppConstants.appData.storeReference!))
//     {
//       accessOptions =
//
//       [
//         //Copy
//         {
//           'title': AppStrings.copyPostLink,
//           'onTap': () {
//             Navigator.pop(context);
//             CommonMethods.copyText(context, AppLinkCreateService().createPostLink(postReference: postDetail.postOrCommentReference!));
//           },
//         },
//         //Edit
//         {
//           'title': AppStrings.editPost.toLowerCase(),
//           'onTap': () {
//             Navigator.pop(context);
//             goToEditPost(postDetail: postDetail);
//
//
//           },
//         },
//         //Delete post
//         {
//           'title': AppStrings.deletePost,
//           'onTap': () {
//             Navigator.pop(context);
//             confirmDelete(postDetail: postDetail);
//
//           },
//         },
//         // Add more options if needed
//       ];
//     }
//     else{
//       accessOptions = [
//         {
//           'title': AppStrings.reportThePost,
//           'onTap': () {
//             Navigator.pop(context);
//             // Navigator.pop(context);
//             var screen = ReportScreen(
//               reference: postDetail.postOrCommentReference!,
//               isProduct: true,
//             );
//             var route = MaterialPageRoute(builder: (context) => screen);
//             Navigator.push(context, route);
//
// //endregion
//
//           },
//         },
//       ];
//
//     }
//
//     CommonMethods.accessBottomSheet(screen: ShareAccessBottomSheet(accessOptions: accessOptions), context: context,);
//
//   }
//   //endregion

  //region Confirm delete
  Future confirmDelete({required PostDetail postDetail}) {
    return CommonMethods.appDialogBox(
        context: context,
        widget: OkayAndCancelDialogScreen(
          onTapSecondButton: () {
            deletePostAndComment(postDetail: postDetail);
          },
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.areYouSureWantsToDelete,
          firstButtonName: "Cancel",
          secondButtonName: "Delete",
        ));
  }
//endregion

  //region On Tap Share
  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: AppLinkCreateService()
            .createPostLink(postReference: postDetail.postOrCommentReference!),
        imageLink: postDetail.images!.isEmpty
            ? null
            : postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        entityType: EntityType.POST,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.handle,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,
        message: "Check out this post",
      ),
      context: context,
    );
    //
    // showModalBottomSheet(
    //     context: context,
    //     isScrollControlled: true,
    //     enableDrag: true,
    //     backgroundColor: AppColors.appWhite,
    //     shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(20), topLeft: Radius.circular(20))),
    //     builder: (context) {
    //       return SingleChildScrollView(
    //           padding: EdgeInsets.zero,
    //           child: Container(
    //             child: Column(
    //               children: [
    //                 Container(
    //                   margin: const EdgeInsets.symmetric(vertical: 20),
    //                   child: Column(
    //                     children: [
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                       verticalSizedBox(10),
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                     ],
    //                   ),
    //                 ),
    //                 BuyerProductShareScreen(url: link, imageLink:imageUrl,),
    //               ],
    //             ),
    //           ));
    //     }).then((value) {
    //   // if (value == null) return;
    //   // supportFilterModel = value;
    //   // applyFilter();
    // });
  }
  //endregion

  //region Go to single post view
  void goToSinglePostView({required String postReference}) {
    var screen = SinglePostViewScreen(
      postReference: postReference,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Go to liked user or store
  void goToLikedUsedOrStoreScreen({required String postReference}) {
    var screen = LikedUserOrStoreScreen(
      contentReference: postReference,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

//region Show Tagged Items Bottom Sheet
  void showTaggedItemsBottomSheet({required String postReference}) async {
    try {
      // Call API to get tagged object details
      PostTaggedObjectsResponse response =
          await PostService().getPostTaggedObjectDetails(
        postReference: postReference,
      );

      debugPrint("Tagged objects API response: ${response.message}");
      debugPrint("Tagged objects data count: ${response.data?.length ?? 0}");

      // Check if context is still mounted before proceeding
      if (!context.mounted) return;

      if (response.data != null && response.data!.isNotEmpty) {
        // Get all tagged objects using the helper method
        Map<String, List<TaggedObject>> allTaggedObjects =
            response.data!.first.getAllTaggedObjects();

        debugPrint(
            "Products count: ${allTaggedObjects['products']?.length ?? 0}");
        debugPrint("Stores count: ${allTaggedObjects['stores']?.length ?? 0}");
        debugPrint("Users count: ${allTaggedObjects['users']?.length ?? 0}");

        // Convert TaggedObject to SuggestionItem for reusing existing UI
        List<SuggestionItem> allTaggedItems = [];

        // Add products
        for (var product in allTaggedObjects['products']!) {
          allTaggedItems.add(SuggestionItem(
            type: 'PRODUCT',
            reference: product.reference,
            primaryText: product.name,
            secondaryText: product.name,
            imageUrl: product.icon,
            storeHandle: product.handle,
            storeName: product.name,
          ));
        }

        // Add stores
        for (var store in allTaggedObjects['stores']!) {
          allTaggedItems.add(SuggestionItem(
            type: 'STORE',
            reference: store.reference,
            primaryText: store.handle,
            secondaryText: store.name,
            imageUrl: store.icon,
          ));
        }

        // Add users
        for (var user in allTaggedObjects['users']!) {
          allTaggedItems.add(SuggestionItem(
            type: 'USER',
            reference: user.reference,
            primaryText: user.handle,
            secondaryText: user.name,
            imageUrl: user.icon,
          ));
        }

        // Show bottom sheet with tagged items
        if (allTaggedItems.isNotEmpty) {
          CommonMethods.accessBottomSheet(
            screen: _TaggedItemsBottomSheet(taggedItems: allTaggedItems),
            context: context,
          );
        } else {
          CommonMethods.toastMessage("No tagged items found", context);
        }
      } else {
        CommonMethods.toastMessage("No tagged items found", context);
      }
    } on ApiErrorResponseMessage catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
    }
  }
//endregion

//region Dispose
  void dispose() {
    postCardCtrl.close();
  }
//endregion
}

// Tagged Items Bottom Sheet Widget
class _TaggedItemsBottomSheet extends StatelessWidget {
  final List<SuggestionItem> taggedItems;

  const _TaggedItemsBottomSheet({required this.taggedItems});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: AppColors.disableBlack.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Title
          Text(
            'Tagged Items',
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          const SizedBox(height: 16),
          // Tagged items widget
          TaggedItemsWidget(
            taggedItems: taggedItems,
            showRemoveButton: false,
            showTabs: true,
          ),
        ],
      ),
    );
  }
}
