import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:base32/base32.dart';

import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:swadesic/features/navigation/navigation_router.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/contact_info_dialog/contact_info_dialog.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/features/common_buyer_seller_screen/celebration/celebration.dart';
import 'package:swadesic/features/common_buyer_seller_screen/maintenance/maintenance_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/profile_image_view/profile_image_view.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_store_and_profile/share_store_and_profile_screen.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_bloc.dart';
import 'package:swadesic/features/splash/splash_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/features/widgets/download_app/download_app.dart';
import 'package:swadesic/model/app_data/app_data.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/hold_visited_references/hold_visited_references.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/return_windows_calculate_model.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/cache_storage/storage_keys.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/user_device_detail_services/user_device_detail_service.dart';
import 'package:swadesic/services/user_rewards_and_invitees_service/user_reward_and_invitees_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_common_methods/app_conditios_check.dart';
import 'package:swadesic/util/app_common_methods/app_fix_format.dart';
import 'package:swadesic/util/app_common_methods/app_validation.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'dart:ui' as ui;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:swadesic/env.dart';

/*
1. Close keyboard
2. Encode to base 32
3. Decode from base 32
4. Product share link generate
5. Comment share link generate
6. Go to share store and profile screen
7. Check is in follower list
8. Check app in mobile view
9. Switch to seller
10. Switch to buyer
11. Toast message
12. Format mobile number
13. Convert to sentence format
14. Get device id
15. Calculate web padding

17. Is web
18. Get epoc time
19. Is local image
20. By reference is buyer
21. Check is user by reference




11. Toast Message
 */
class CommonMethods with AppConditionsCheck, AppFixFormat, AppValidationCheck {
  ///0. Validate email address
  //region Validate email
  static bool validateEmail(String email) {
    // Regular expression pattern for email validation
    final RegExp emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      caseSensitive: false,
      multiLine: false,
    );
    return emailRegex.hasMatch(email);
  }

  //endregion
  ///1. Close keyboard
  //region Close Keyboard
  static closeKeyboard(context) {
    FocusScope.of(context).unfocus();
  }

  //endregion

  ///2. Encode to base 32
  //region Encode to base 32
  static String encodeBase32(String inputString) {
    const base32Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';

    try {
      List<int> bytes = utf8.encode(inputString);
      StringBuffer result = StringBuffer();
      int buffer = 0;
      int bufferLength = 0;

      for (int value in bytes) {
        buffer = (buffer << 8) | value;
        bufferLength += 8;

        while (bufferLength >= 5) {
          int index = (buffer >> (bufferLength - 5)) & 0x1F;
          result.write(base32Chars[index]);
          bufferLength -= 5;
        }
      }

      if (bufferLength > 0) {
        int index = (buffer << (5 - bufferLength)) & 0x1F;
        result.write(base32Chars[index]);
      }

      // Add padding if necessary
      int paddingLength = (8 - (result.length % 8)) % 8;
      for (int i = 0; i < paddingLength; i++) {
        result.write('=');
      }

      return result.toString();
    } catch (exception) {
      return "";
    }
  }

  //endregion

  ///3. Decode from base 32
  //region Decode from base 32
  static String decodeBase32(String encodedString) {
    try {
      const base32Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';

      encodedString = encodedString.replaceAll('=', ''); // Remove padding
      encodedString = encodedString.toUpperCase(); // Convert to uppercase

      int buffer = 0;
      int bufferLength = 0;
      StringBuffer result = StringBuffer();

      for (int char in encodedString.runes) {
        int charIndex = base32Chars.indexOf(String.fromCharCode(char));
        if (charIndex == -1) {
          throw FormatException(
              'Invalid Base32 character: ${String.fromCharCode(char)}');
        }

        buffer = (buffer << 5) | charIndex;
        bufferLength += 5;

        if (bufferLength >= 8) {
          result.writeCharCode((buffer >> (bufferLength - 8)) & 0xFF);
          bufferLength -= 8;
        }
      }

      if (bufferLength >= 5) {
        throw const FormatException('Incomplete Base32 data');
      }

      return result.toString();
    } catch (exception) {
      return encodedString;
      return "";
    }
  }

  //endregion

  //region Can decode base 32
  static bool canDecodeBase32(String encodedString) {
    try {
      utf8.decode(base32.decode(encodedString));

      return true;

      // const base32Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
      // final paddedLength = ((encodedString.length + 7) ~/ 8) * 8;
      // encodedString = encodedString.toUpperCase().padRight(paddedLength, '=');
      //
      // int buffer = 0;
      // int bufferLength = 0;
      //
      // for (int char in encodedString.runes) {
      //   int charIndex = base32Chars.indexOf(String.fromCharCode(char));
      //   if (charIndex == -1) {
      //     return false;
      //   }
      //
      //   buffer = (buffer << 5) | charIndex;
      //   bufferLength += 5;
      //
      //   if (bufferLength >= 8) {
      //     bufferLength -= 8;
      //   }
      // }
      //
      // return bufferLength < 5;
    } catch (exception) {
      return false;
    }
  }

  //endregion

  ///6. Go to share store and profile screen
  //region Go to share store and profile
  static goToShareStoreAndProfile(
      {StoreInfo? storeInfo,
      UserDetail? userDetail,
      required BuildContext context}) {
    var screen = ShareStoreAndProfileScreen(
      storeInfo: storeInfo,
      userDetail: userDetail,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  ///7. Check is in follower list
  //region Is in followers list
  static bool isInFollowerList(
      {required Followers followers, required String userAndStoreReference}) {
    bool inFollowerList = false;
    //Check in user list
    if (userAndStoreReference.split("").first.contains('U')) {
      inFollowerList =
          followers.users!.any((e) => e.userReference == userAndStoreReference);
    }
    //Check in store list
    else {
      inFollowerList = followers.stores!
          .any((e) => e.storeReference == userAndStoreReference);
    }
    //return follower status
    return inFollowerList;
  }

  //endregion

  ///8. Check app in mobile view
  //region Check is app in mobile view
  static bool checkIsAppInMobileView() {
    // Use kIsWeb constant to check if the app is running in a web view
    if (kIsWeb) {
      // You can further customize this logic based on screen size or other criteria
      // to determine if it's a "mobile" view on the web platform.
      // For example, you might consider screen width to define what's "mobile" on the web.
      // For simplicity, this example treats all web views as non-mobile.
      return false;
    } else {
      return true;
    }
  }

  //endregion

  ///9. Switch to seller
  //region Switch to seller
  static switchToSeller(
      {required String storeReference,
      required int storeId,
      required BuildContext context}) async {
    //Clear references from singleton
    HoldVisitedReferences().clearReferences();
    //Mark user view is false
    AppConstants.appData.isUserView = false;
    //Mark store view is true
    AppConstants.appData.isStoreView = true;
    //Store reference
    AppConstants.appData.storeReference = storeReference;
    //Add store id
    AppConstants.appData.storeId = storeId;
    //Add all data to share pref
    await AppDataService().addAppData();

    //Set seller navigation to Profile tab (index 5)
    AppConstants.storePersistentTabController.index = 5;
    debugPrint('switchToSeller: Set storePersistentTabController.index to 5');

    //Pop all the screen pushed except bottom navigation
    AppConstants.globalNavigator.currentState!
        .popUntil((route) => route.isFirst);

    //Push and replace previous bottom navigation
    if (context.mounted) {
      Navigator.pushReplacement(
        AppConstants.userStoreCommonBottomNavigationContext,
        MaterialPageRoute(builder: (context) => const NavigationRouter()),
      );
    } else {
      // Fallback to global navigator if context is not mounted
      Navigator.pushReplacement(
        AppConstants.globalNavigator.currentContext ??
            AppConstants.userStoreCommonBottomNavigationContext,
        MaterialPageRoute(builder: (context) => const NavigationRouter()),
      );
    }
  }

  //endregion

  ///10. Switch to buyer
  //region Switch to buyer
  static switchToBuyer({required BuildContext context}) async {
    //Clear references from singleton
    HoldVisitedReferences().clearReferences();
    //Mark user view is true
    AppConstants.appData.isUserView = true;
    //Mark store view is false
    AppConstants.appData.isStoreView = false;
    //Store reference
    AppConstants.appData.storeReference = null;
    //Add store id
    AppConstants.appData.storeId = null;
    //Add all data to share pref
    await AppDataService().addAppData();

    //Set buyer navigation to Home tab (index 0)
    AppConstants.userPersistentTabController.index = 0;
    debugPrint('switchToBuyer: Set userPersistentTabController.index to 0');

    //Push and replace previous bottom navigation
    if (context.mounted) {
      Navigator.pushReplacement(
        AppConstants.userStoreCommonBottomNavigationContext,
        MaterialPageRoute(builder: (context) => const NavigationRouter()),
      );
    } else {
      // Fallback to global navigator if context is not mounted
      Navigator.pushReplacement(
        AppConstants.globalNavigator.currentContext ??
            AppConstants.userStoreCommonBottomNavigationContext,
        MaterialPageRoute(builder: (context) => const NavigationRouter()),
      );
    }
  }

  //endregion

  //region Switch to buyer
  static switchToBuyerStorePreview(
      {required BuildContext context, required String storeReference}) async {
    //Switch to buyer
    CommonMethods.switchToBuyer(context: context);
    //Push to product screen immediately after account switch
    var screen = BuyerViewStoreScreen(storeReference: storeReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
//endregion

  ///11. Toast message
  //region Toast Message
  static void toastMessage(String message, BuildContext? context,
      {int toastShowTimer = 2, bool commonErrorMessage = false}) {
    FToast fToast = FToast();

    //If static user then return
    // if (CommonMethods().isStaticUser()) {
    //   return;
    // }

    //Disable toast message in prod env for common error message
    if (commonErrorMessage &&
        AppConstants.appCurrentEnvironment == Environment.prod) {
      return;
    }

    fToast.init(context ?? AppConstants.userStoreCommonBottomNavigationContext);

    // Close toast
    fToast.removeCustomToast();

    fToast.showToast(
        fadeDuration: const Duration(milliseconds: 100),
        // gravity:ToastGravity.BOTTOM ,
        child: GestureDetector(
          // padding: EdgeInsets.zero,

          onHorizontalDragUpdate: (dragDetail) {
            ///Todo test to

            fToast.removeCustomToast();
          },
          onTap: () {
            // Close toast
            fToast.removeCustomToast();
          },
          child: SafeArea(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppColors.appRichBlack, //.withOpacity(0.9),
                // border: Border.all(color: AppColors.appBlack),
                // boxShadow: AppColors.toastMessageShadow
              ),
              width: double.infinity,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      message,
                      style: AppTextStyle.heading4SemiBold(
                          textColor: message == AppStrings.noInternet
                              ? AppColors.red
                              : AppColors.appWhite),
                    ),
                    // child: Text(
                    //   AppStrings.noInternet,
                    //   style: AppTextStyle.heading4SemiBold(textColor: AppStrings.noInternet==AppStrings.noInternet?AppColors.red:AppColors.appBlack),
                    // ),
                  ),
                ],
              ),
            ),
          ),
        ),
        toastDuration: Duration(seconds: toastShowTimer),
        positionedToastBuilder: (context, child) {
          return Positioned(
            top: 45,
            left: 7,
            right: 7,
            child: child,
          );
        });
  }

  //endregion

  //region App snack bar
  static void appSnackBar(String message, {int toastShowTimer = 2}) {
    FToast fToast = FToast();

    fToast.init(AppConstants.userStoreCommonBottomNavigationContext);

    // Close toast
    fToast.removeCustomToast();

    fToast.showToast(
        // gravity:ToastGravity.BOTTOM ,
        child: GestureDetector(
          // padding: EdgeInsets.zero,

          onHorizontalDragUpdate: (dragDetail) {
            ///Todo test to

            fToast.removeCustomToast();
          },
          onTap: () {
            // Close toast
            fToast.removeCustomToast();
          },
          child: SafeArea(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100),
                  color: AppColors.appWhite.withOpacity(0.9),
                  boxShadow: AppColors.toastMessageShadow),
              child: Text(
                message,
                style: AppTextStyle.heading4SemiBold(
                    textColor: message == AppStrings.noInternet
                        ? AppColors.red
                        : AppColors.appBlack),
              ),
            ),
          ),
        ),
        toastDuration: Duration(seconds: toastShowTimer),
        positionedToastBuilder: (context, child) {
          return Positioned(
            left: 7,
            right: 7,
            bottom: kToolbarHeight * 2,
            child: child,
          );
        });
  } //endregion

  //region Post success
  static void postSuccess(String message, BuildContext? context,
      {int toastShowTimer = 2}) {
    FToast fToast = FToast();

    fToast.init(context ?? AppConstants.userStoreCommonBottomNavigationContext);

    // Close toast
    fToast.removeCustomToast();

    fToast.showToast(
        fadeDuration:
            const Duration(milliseconds: 100), // for faster appearance
        // gravity:ToastGravity.BOTTOM ,
        child: GestureDetector(
          // padding: EdgeInsets.zero,

          onHorizontalDragUpdate: (dragDetail) {
            ///Todo test to

            fToast.removeCustomToast();
          },
          onTap: () {
            // Close toast
            fToast.removeCustomToast();
          },
          child: Container(
            margin: EdgeInsets.only(top: kToolbarHeight),
            padding: EdgeInsets.symmetric(vertical: 10),
            decoration: BoxDecoration(
                color: AppColors.writingBlack0,
                boxShadow: AppColors.toastMessageShadow),
            width: double.infinity,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    message,
                    textAlign: TextAlign.center,
                    style: AppTextStyle.heading4SemiBold(
                        textColor: AppColors.appWhite),
                  ),
                ),
              ],
            ),
          ),
        ),
        toastDuration: Duration(seconds: toastShowTimer),
        positionedToastBuilder: (context, child) {
          return Positioned(
            top: 30,
            left: 0,
            right: 0,
            child: child,
          );
        });
  }

  //endregion

  ///12. Format mobile number
  //region Format mobile number
  static String formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.length != 13 || !phoneNumber.startsWith('+')) {
      // Invalid phone number format, return as it is
      return phoneNumber;
    }

    String countryCode = phoneNumber.substring(0, 3); // Extracting '+91'
    String restOfNumber = phoneNumber.substring(3); // Extracting '8076219463'

    String formattedNumber =
        '$countryCode ${restOfNumber.substring(0, 5)} ${restOfNumber.substring(5)}';
    return formattedNumber;
  }

  //endregion

  ///13. Convert to sentence format
  //region Convert to sentence format
  static String? convertToSentenceFormat({String? input}) {
    if (input == null) {
      return null;
    } else if (input.isEmpty) {
      return '';
    } else {
      // Convert the string to lowercase and capitalize the first word
      String formattedString = input.toLowerCase();
      formattedString = formattedString.substring(0, 1).toUpperCase() +
          formattedString.substring(1);
      return formattedString;
    }
  }

  //endregion

  /// Converts a UTC timestamp string to IST.
  /// If an error occurs, it returns the original UTC timestamp.
  //region Convert utc to ist
  static String? convertUtcToIst(String? utcTimestamp) {
    if (utcTimestamp == null) return null;
    try {
      // Parse the UTC timestamp
      DateTime utcTime = DateTime.parse(utcTimestamp).toUtc();
      // Add IST offset (UTC +5:30)
      DateTime istTime = utcTime.add(const Duration(hours: 5, minutes: 30));
      // Format the IST timestamp
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(istTime);
    } catch (e) {
      //print('Error converting UTC to IST: $e');
      // Return the original timestamp if something goes wrong
      return utcTimestamp;
    }
  }
  //endregion

  ///14. Get device id
  //region Get device id
  static Future<String> getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    String deviceId = '';
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceId = androidInfo.id;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceId = iosInfo.identifierForVendor!;
    }
    return deviceId;
  }

  //endregion

  ///15. Calculate web padding
  //region Calculate web
  static double calculateWebPadding({required BuildContext context}) {
    // Device width
    double deviceWidth = MediaQuery.of(context).size.width;
    //App width to global
    AppConstants.appWidth = deviceWidth;
    // Total padding each side
    double paddingOnEachSide = 0.0;

    //If mobile
    if (!kIsWeb) {
      return paddingOnEachSide;
    }

    // If mobile (width < 600), return 0 padding
    if (deviceWidth < 600) {
      return paddingOnEachSide;
    }
    // For tablets and larger screens
    else {
      // Calculate total padding needed
      double totalPadding = deviceWidth - 450;
      // Calculate padding for each side
      paddingOnEachSide = totalPadding / 2;
      return paddingOnEachSide;
    }
  }

  //endregion

  //region calculate web width
  static double calculateWebWidth({required BuildContext context}) {
    // Device width
    double deviceWidth = MediaQuery.of(context).size.width;
    //App width to global
    AppConstants.appWidth = deviceWidth;
    // Total padding each side
    double paddingOnEachSide = 0.0;

    // If mobile (width < 600), return 0 padding
    if (deviceWidth < 600) {
      return deviceWidth;
    }
    // For tablets and larger screens
    else {
      // Calculate total padding needed
      double totalPadding = deviceWidth - 450;
      // Calculate padding for each side
      paddingOnEachSide = totalPadding / 2;
      // Subtract padding from both sides to get the remaining width
      return deviceWidth - (paddingOnEachSide * 2);
    }
  }

  //endregion

  ///16. Get device height and width
  static Map<String, dynamic> getDeviceHeightAndWidth(
      {required BuildContext context}) {
    double deviceHeight = MediaQuery.of(context).size.height;
    double deviceWidth = MediaQuery.of(context).size.width;

    return {
      'height': deviceHeight,
      'width': deviceWidth,
    };
  }

  ///17. Is web
  //region In web
  static bool isWeb() {
    if (kIsWeb) {
      return true;
    } else {
      return false;
    }
  }

  //endregion

  ///18. Get epoc time
  //region Get epoc time
  static String getEpocTime() {
    DateTime now = DateTime.now();
    int epochTimeInSeconds = now.millisecondsSinceEpoch ~/ 1000;
    return epochTimeInSeconds.toString();
  }

  //endregion

  ///19. Is local image
  //region Is local image
  static Future<bool> isLocalFile({required String imagePath}) async {
    File file = File(imagePath);
    return await file.exists();
  }

  //endregion

  ///20. By reference is user
  //region By reference is user
  static byReferenceIsUSer({required String reference}) {
    if (reference.split("").first == "U") {
      return true;
    } else {
      return false;
    }
  }

  //endregion

  ///21. Check is user by reference
  //region Check is user by reference
  static bool checkIsUserByReference({required String reference}) {
    //If start with U then true else false
    return reference.startsWith("U") ? true : false;
  }

  //endregion

  ///22. Logout
  //region Logout
  static void logOut() async {
    //Delete FCM token if user is on mobile only
    !CommonMethods.isWeb()
        ? await UserDeviceDetailService().deleteDeviceAndFcmDetail(
            deviceId: await CommonMethods.getDeviceId())
        : null;
    //Cart quantity
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(
            AppConstants.userStoreCommonBottomNavigationContext,
            listen: false);

    //Logged in user
    LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(
            AppConstants.userStoreCommonBottomNavigationContext,
            listen: false);

    //Clear share preference
    CacheStorageService().removeItem(StorageKeys.userId);
    CacheStorageService().removeItem(StorageKeys.userReference);
    CacheStorageService().removeItem(StorageKeys.shoppingCartAddress);
    CacheStorageService().removeItem(StorageKeys.shoppingCartMessage);
    CacheStorageService().removeItem(StorageKeys.appdataKey);
    //Clear cart
    // shoppingCartQuantityDataModel.productReferenceList.clear();
    shoppingCartQuantityDataModel.productReferenceList.clear();
    //Clear user data in global
    AppConstants.appData = AppData(baseUrl: AppConstants.baseUrl);
    //Switch user bottom navigation to the 1st
    // AppConstants.userPersistentTabController.jumpToTab(0);
    AppConstants.userPersistentTabController.jumpToTab(0);
    //Clear user logged in user info
    loggedInUserInfoDataModel.switchToStaticUser();
    //Push and pop all screen behind
    Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
        .pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const SplashScreen()),
            (Route<dynamic> route) => false);
  }

//endregion

  //region Check app update
  static void appUpdateCheck() {
    //If platform is not web and not even debug  then check
    if (!kIsWeb && !kDebugMode) {
      InAppUpdate.checkForUpdate().then((updateInfo) {
        if (updateInfo.updateAvailability ==
            UpdateAvailability.updateAvailable) {
          //Change the value to true

          // if (updateInfo.immediateUpdateAllowed) {
          //   // Perform immediate update
          //   InAppUpdate.performImmediateUpdate().then((appUpdateResult) {
          //     if (appUpdateResult == AppUpdateResult.success) {
          //       //App Update successful
          //     }
          //   });
          // }
          if (updateInfo.flexibleUpdateAllowed) {
            //Perform flexible update
            InAppUpdate.startFlexibleUpdate().then((appUpdateResult) {
              if (appUpdateResult == AppUpdateResult.success) {
                //App Update successful
                InAppUpdate.completeFlexibleUpdate();
              }
            });
          }
        }
      });
    }
  }

//endregion

  //region Share
  static share(String data) async {
    await Share.share(data);
  }

//endregion

  static void shareOnFacebook(String url) async {
    // Replace 'https://www.facebook.com/' with your Facebook profile or page URL
    var facebookUrl = 'https://www.facebook.com/';

    if (await canLaunch(facebookUrl)) {
      await launch(facebookUrl + '?u=$url');
    } else {
      throw 'Could not launch $facebookUrl';
    }
  }

  //region Convert String date time slash formatto dd-mm-yyyy
  static String convertStringDateTimeSlashFormat(String dateTime) {
    DateTime tempDate = DateFormat("dd/MM/yyyy HH:mm:ss").parse(dateTime);
    //print(tempDate.toString());
    String tempDate2 = DateFormat("dd-MM-yyyy, h:mm a").format(tempDate);
    return tempDate2.toString();
  }

  //endregion

  //region Calculate return window
  static ReturnWindowsCalculateModel calculateReturnWindows(
      {required SubOrder subOrder}) {
    //Convert delivered date to date time
    DateTime deliveredDateTime =
        DateFormat("dd/MM/yyyy hh:mm:ss").parse(subOrder.deliveredDate!);
    // DateTime deliveredDateTime = DateTime(2023,1,01,00,00,00);
    //Return accept date time
    DateTime returnAcceptDateTime = DateFormat("dd-MM-yyyy hh:mm:ss").parse(
        daysToDate(
            daysCount: subOrder.returnDays!, countFrom: deliveredDateTime));
    //  DateTime returnAcceptDateTime = DateFormat("dd-MM-yyyy hh:mm:ss").parse(daysToDate(daysCount:20,deliveredDate: DateTime(2023,5,1)),);
    //Current date time
    DateTime currentDate = DateTime.now();
    //Calculate duration
    Duration duration = returnAcceptDateTime.difference(
        DateTime(currentDate.year, currentDate.month, currentDate.day));
    //print("The final data is ${duration.inDays}");
    //Calculate day difference
    int dayDiff = duration.inDays;

    ///If day return days is 0
    if (subOrder.returnDays == 0) {
      return ReturnWindowsCalculateModel(
          isReturnDateComing: false,
          isReturnDatePassed: false,
          returnMessage: "This product doesn’t support return",
          isReturnAvailable: false,
          returnDate: DateFormat('dd-MM-yyyy').format(returnAcceptDateTime));
    }

    ///If duration is 0 means open
    if (dayDiff == 0) {
      return ReturnWindowsCalculateModel(
          isReturnDateComing: true,
          isReturnDatePassed: false,
          returnMessage: "Return window will close today ",
          isReturnAvailable: true,
          returnDate: DateFormat('dd-MM-yyyy').format(returnAcceptDateTime));
    }

    ///If duration contain negative value
    if (dayDiff.isNegative) {
      return ReturnWindowsCalculateModel(
          isReturnDateComing: false,
          isReturnDatePassed: true,
          returnMessage: "Return window closed on ",
          isReturnAvailable: true,
          returnDate: DateFormat('dd-MM-yyyy').format(returnAcceptDateTime));
    }

    ///If duration is positive value
    if (!dayDiff.isNegative) {
      return ReturnWindowsCalculateModel(
          isReturnDateComing: true,
          isReturnDatePassed: false,
          returnMessage: "Return window closes by ",
          isReturnAvailable: true,
          returnDate: DateFormat('dd-MM-yyyy').format(returnAcceptDateTime));
    }

    return ReturnWindowsCalculateModel(
        isReturnAvailable: true,
        isReturnDateComing: true,
        isReturnDatePassed: true,
        returnMessage: "Null",
        returnDate: DateFormat('dd-MM-yyyy').format(returnAcceptDateTime));
  }

  //endregion

  //region Payout release date
  static String payOutReleaseDate({required String data}) {
    try {
      if (data.isEmpty) {
        return "";
      }
      DateTime dataDateTime = DateFormat("yyyy-MM-dd").parse(data);
      String dataDate = DateFormat('dd-MM-yyyy').format(dataDateTime);
      return dataDate;
    } catch (e) {
      // Return empty string if there's any error parsing the date
      return "";
    }
  }

  //endregion

  //region Days to date
  static String daysToDate(
      {required int daysCount, required DateTime countFrom}) {
    //Delivered date time
    DateTime deliveredDateTime = countFrom;

    //Adding days to the current date time
    DateTime futureDate = deliveredDateTime.add(Duration(days: daysCount));

    // Formatting the date
    String formattedDate = DateFormat('dd-MM-yyyy 00:00:00').format(futureDate);

    return formattedDate;
  }

  //endregion

  //region Date time to String month
  static String dateTimeToStringMonth({required String date}) {
    DateTime dataDateTime;

    try {
      // Check if data contain / then convert in different format
      if (date.contains("/")) {
        dataDateTime = DateFormat("dd/MM/yyyy hh:mm:ss").parse(date);
      } else if (date.contains("T") ||
          date.contains("+") ||
          date.contains("Z")) {
        // Handle ISO 8601 format (e.g., "2025-06-10 21:09:49.307445+05:30" or "2025-06-10T21:09:49.307445+05:30")
        dataDateTime = DateTime.parse(date);
      } else {
        dataDateTime = DateFormat("dd:MM:yyyy hh:mm:ss").parse(date);
      }
    } catch (e) {
      // If parsing fails, try DateTime.parse as fallback
      try {
        dataDateTime = DateTime.parse(date);
      } catch (e2) {
        // If all parsing fails, use current date as fallback
        dataDateTime = DateTime.now();
      }
    }

    String formattedDate = DateFormat.yMMMMd().format(dataDateTime);

    return formattedDate;
  }

//endregion

  //region calculate days
  static List<String> dateTimeAmPm({required String date}) {
    int days;
    String daysAndAGo;
    DateTime dataDateTime;

    try {
      //Convert String date time to Date time
      //Check if data contain / then convert in different format
      if (date.contains("/")) {
        dataDateTime = DateFormat("dd/MM/yyyy hh:mm:ss").parse(date);
      } else if (date.contains("T") ||
          date.contains("+") ||
          date.contains("Z")) {
        // Handle ISO 8601 format (e.g., "2025-06-10 21:09:49.307445+05:30" or "2025-06-10T21:09:49.307445+05:30")
        dataDateTime = DateTime.parse(date);
      } else {
        dataDateTime = DateFormat("dd:MM:yyyy hh:mm:ss").parse(date);
      }
    } catch (e) {
      // If parsing fails, try DateTime.parse as fallback
      try {
        dataDateTime = DateTime.parse(date);
      } catch (e2) {
        // If all parsing fails, use current date as fallback
        dataDateTime = DateTime.now();
      }
    }

    String dataDate = DateFormat('dd-MM-yyyy').format(dataDateTime);
    //Convert data time to time format with AM and PM
    String dataTime = DateFormat.jm().format(DateFormat("hh:mm:ss").parse(
        "${dataDateTime.hour}:${dataDateTime.minute}:${dataDateTime.second}"));

    //Current date time
    DateTime dateTimeNow = DateTime.now();
    //Compare and calculate days
    days = dateTimeNow.difference(dataDateTime).inDays;
    //print(days);

    //Today,yesterday,last 7 day and past.
    // if (days == 0) return ["0", "Today"];
    // if (days == 1) return ["1", "Yesterday"];
    // if (days > 1 && days < 8) return ["$days", "Last 7 days"];
    return ["$days", dataDate, dataTime];
  }

//endregion

  //region Get month difference
  static int getMonthDifference(
      {required DateTime initialDate, required DateTime endDate}) {
    return (endDate.year - initialDate.year) * 12 +
        (endDate.month - initialDate.month) +
        1;
  }

  //endregion

  //region Close Screen
  static closeScreen(BuildContext context) {
    Navigator.pop(context);
  }

  //endregion

  //region Pin Code Validation
  bool pinCodeValidation(String pinCode) {
    String pin = pinCode.toString();
    bool pinValid = RegExp(r'^[1-9][0-9]{5}$').hasMatch(pin);
    return pinValid;
  }

  //endregion

  //region snackBar
  // static void toastMessage(String message, BuildContext context) {
  //   ScaffoldMessenger.of(context).hideCurrentSnackBar();
  //   var snackBar = SnackBar(
  //     behavior:SnackBarBehavior.floating ,
  //     // behavior: SnackBarBehavior.floating,
  //     // margin: EdgeInsets.only(bottom:100),
  //
  //
  //     // showCloseIcon: true,
  //     content: Row(
  //       mainAxisSize: MainAxisSize.min,
  //       children: [
  //         SvgPicture.asset(AppImages.smileEmoji),
  //         horizontalSizedBox(5),
  //         Expanded(
  //             child: Text(
  //           message,
  //           style: const TextStyle(fontFamily: "LatoRegular", fontSize: 15, fontWeight: FontWeight.w400, color: AppColors.writingColor2),
  //         )),
  //       ],
  //     ),
  //     backgroundColor: AppColors.lightestGrey,
  //   );
  //   ScaffoldMessenger.of(context).showSnackBar(snackBar);
  // }
//endregion

  //region Capital first letter
  static String capitalFirstLetter({required String? value}) {
    //If value is null or empty then return empty string
    if (value == null || value.isEmpty) {
      return "";
    }
    List<String> data = value.split("");
    if (data.isEmpty) {
      return "";
    }
    data[0] = data.first.toUpperCase();
    return data.join("");
  }

  //endregion

  //region Open app web view
  static openAppWebView(
      {required String webUrl, required BuildContext context}) async {
    //If web view then launch the url
    if (kIsWeb) {
      launch(webUrl);
      return;
    }
    if (webUrl.isEmpty) {
      return toastMessage("Invalid url", context);
    }
    String myUrl = fixUrl(url: webUrl);
    //print(myUrl);
    var screen = AppWebView(
      url: myUrl,
    );
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);

    // //print(webUrl);
    // // String url = webUrl;
    // Uri url = Uri.parse(webUrl);
    // //print(url);
    // try {
    //   await launchUrl(url);
    //   // await launch(url);
    // } catch (e) {
    //   //print(e);
    // }
  }

//endregion

  //region Open url
  static void openUrl({required String url}) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Could not launch $url';
    }
  }

  //endregion

  //region Fix url
  static String fixUrl({required String url}) {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      // If the URL doesn't start with either http:// or https://, add https://
      return 'https://$url';
    }
    return url;

    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url; // URL already contains http or https
    } else {
      return 'https://$url'; // Add https to the URL
    }
  }

  //endregion

//region Convert Time
  String convertTime(String time) {
    // String date = "2022:08:25 19:42:56";
    // DateTime.parse(date);
    // return DateFormat("EEE, d MMM yyyy HH:mm:ss").format(dt);
    return "jij";
  }

//endregion

//region Copy
  static copyText(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    toastMessage(AppStrings.copiedToClipBoard, context);

    // FlutterClipboard.copy(text).then((result) {
    //   snackBar(AppStrings.copiedToClipBoard, context);
    // });
  }

//endregion

  //region Paste
  static Future<String> pasteText() async {
    ClipboardData? data = await Clipboard.getData('text/plain');
    return data!.text!;
  }

  //endregion

//region Open bottom sheet for buyer and seller order
// void sellerAndBuyerOrderBottomSheet(){}
  static Future sellerAndBuyerOrderBottomSheet(
      {required subOrderList,
      required BuildContext context,
      required String title,
      required String subTitle,
      required screen}) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(20), topLeft: Radius.circular(20)),
        ),
        builder: (context) {
          return FractionallySizedBox(
            heightFactor: 0.8,
            child: Column(
              children: [
                //Title
                Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Text(
                      title,
                      style: AppTextStyle.heading1Medium(
                          textColor: AppColors.writingBlack),
                    )),
                //Subtitle
                subTitle == ""
                    ? const SizedBox()
                    : Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 20),
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: appText(subTitle,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppColors.writingColor2,
                                maxLine: 3,
                                fontFamily: AppConstants.rRegular)),
                      ),
                Expanded(child: screen),
              ],
            ),
          );
        }).then((value) {
      //sellerAllOrdersBloc sellerAllOrdersBloc?.getSellerAllOrder(false);
    });
  }

//endregion

  //region Is all order selected
  static bool isAllOrderSelectedSelected(
      {required List<SubOrder> subOrderList}) {
    List<SubOrder> data =
        subOrderList.where((element) => element.isSelected == true).toList();

    if (data.length == subOrderList.length) {
      return true;
    } else {
      return false;
    }
  }

  //endregion

  //region Return how many suborders are selected
  static int returnHowManySubOrdersSelected(
      {required List<SubOrder> subOrderList}) {
    List<SubOrder> data =
        subOrderList.where((element) => element.isSelected == true).toList();
    return data.length;
  }

  //endregion

//region Breakup price
  static String breakupPrice({required String rawBreakupPrice}) {
    String breakupPrice = rawBreakupPrice;
    List<String> finalList = [];

    List<String> breakupPriceSplit = breakupPrice.split("+");

    //print(breakupPriceSplit);

    ///Add rupee and + .
    for (var price in breakupPriceSplit) {
      if (price != breakupPriceSplit.last) {
        finalList.add(" ₹ $price + ");
      }
      if (price == breakupPriceSplit.last) {
        finalList.add(" ₹ $price ");
      }
    }
    //print(finalList.join());

    ///Return final structure

    return finalList.join();
  }

//endregion

//region Open calender
  static Future<String> openCalender(
      {required BuildContext context, required String selectedDateTime}) async {
    DateTime selectedDate = DateFormat("dd-MM-yyyy").parse(selectedDateTime);
    DateTime currentDate = DateTime.now();
    // Check if the selected date is lower than the current date
    if (selectedDate.isBefore(currentDate)) {
      selectedDate = currentDate; // Set selectedDate to current date
    }
    final DateTime? picked = await showDatePicker(
        confirmText: "Save",
        cancelText: "Cancel",
        context: context,
        initialDate: selectedDate,
        firstDate: currentDate,
        lastDate: DateTime(2101),
        builder: (context, child) {
          return Theme(
            data: ThemeData.light(useMaterial3: false).copyWith(
              primaryColor: AppColors.brandBlack,
              colorScheme: ColorScheme.light(primary: AppColors.brandBlack),
              buttonTheme: ButtonThemeData(textTheme: ButtonTextTheme.primary),
            ),
            child: child!,
          );
        });
    if (picked != null && picked != selectedDate) {
      selectedDate = picked;
    } else {
      return selectedDateTime;
    }
    return DateFormat("dd-MM-yyyy").format(selectedDate);
  }

//endregion

  //region Give estimate date
  static String giveMaxDeliveryDate({required List<String> dateTimeList}) {
    // List<String> dates = ["09/05/2023", "09/04/2023", "25/05/2023"];

    List<String> dateTimeList = [];
    //Convert string date time to date time
    for (var data in dateTimeList) {
      dateTimeList.add(DateFormat("dd/MM/yyyy").parse(data).toString());
    }
    DateTime minDateTime = DateTime.parse(dateTimeList[0]);
    DateTime maxDateTime = DateTime.parse(dateTimeList[0]);

    for (int i = 1; i < dateTimeList.length; i++) {
      DateTime currDateTime = DateTime.parse(dateTimeList[i]);
      if (currDateTime.isBefore(minDateTime)) {
        minDateTime = currDateTime;
      }
      if (currDateTime.isAfter(maxDateTime)) {
        maxDateTime = currDateTime;
      }
    }
    return DateFormat('dd-MM-yyyy').format(minDateTime).toString();
    // //print(DateFormat('dd-MM-yyyy').format(minDateTime));
    // //print(DateFormat('dd-MM-yyyy').format(maxDateTime));
  }

  //endregion

  //region Minimum and maximum date time
  static String minimumAndMaximumDateTime(
      {required List<String> dateList, bool isMinimum = true}) {
    DateTime minMaxDateTime;
    List<DateTime> dates = dateList
        .map((dateString) => DateFormat('dd/MM/yyyy').parse(dateString))
        .toList();

    //Minimum
    if (isMinimum) {
      minMaxDateTime = dates
          .reduce((currMin, date) => date.isBefore(currMin) ? date : currMin);
    }
    //Maximum
    else {
      minMaxDateTime = dates
          .reduce((currMin, date) => date.isAfter(currMin) ? date : currMin);
    }

    String formattedDate = DateFormat('dd-MM-yyyy').format(minMaxDateTime);

    return formattedDate;
  }

  //endregion

  //region Average date time
  static String averageDateTime(List<String> dateStrings) {
    if (dateStrings.isEmpty) {
      throw ArgumentError("The list of date strings cannot be empty.");
    }

    // Convert date strings to DateTime objects
    List<DateTime> dateTimeList = dateStrings
        .map((dateString) => DateFormat('dd/MM/yyyy').parse(dateString))
        .toList();

    // Calculate the total milliseconds from the list of DateTime objects
    int totalMilliseconds = dateTimeList
        .map((dateTime) => dateTime.millisecondsSinceEpoch)
        .reduce((sum, milliseconds) => sum + milliseconds);

    // Calculate the average milliseconds
    int averageMilliseconds = totalMilliseconds ~/ dateTimeList.length;

    // Create a new DateTime object from the average milliseconds
    DateTime averageDateTime =
        DateTime.fromMillisecondsSinceEpoch(averageMilliseconds);

    // Format the DateTime as "dd/MM/yyyy"
    String formattedDate = DateFormat('dd/MM/yyyy').format(averageDateTime);

    return formattedDate;
  }

  //endregion

//region Is valid delivery date
  static bool isValidDeliveryDate({required String givenDate}) {
    DateTime currentDate = DateTime.now();
    DateFormat format = DateFormat('dd-MM-yyyy');
    DateTime providedDate = format.parse(givenDate);

    // Set the time components of both dates to the start of the day (midnight)
    currentDate =
        DateTime(currentDate.year, currentDate.month, currentDate.day);
    providedDate =
        DateTime(providedDate.year, providedDate.month, providedDate.day);

    if (providedDate.isBefore(currentDate)) {
      return false;
    } else if (providedDate.isAtSameMomentAs(currentDate)) {
      return true;
    } else {
      return true;
    }
  }

//endregion

  ///Seller
  ///Buyer
//region Buyer close bottom sheet and get all orders
  static void buyerCloseBottomSheetAndGetAllOrders(
      {BuildContext? context,
      required Order store,
      required BuyerMyOrdersBloc buyerMyOrdersBloc}) async {
    ///Get Buyer all order
    await buyerMyOrdersBloc.getBuyerMyOrders();

    ///Close bottom sheet
    ///If the context is empty then return
    if (context != null) {
      Navigator.pop(context);
    }
  }

//endregion

//region Return the file path extensions
  static String returnExtension({required File file}) {
    String extension;
    //Split file path by . and collect extension
    extension = file.path.split(".").last;
    return extension;
  }

//endregion

//region Open Buyer my order Dialog
  static Future openBuyerMyOrderDialog(
      {required SubOrder subOrder,
      required BuildContext context,
      required String heading}) {
    return showDialog(
        context: context,
        useSafeArea: true,
        builder: (_) => AlertDialog(
              scrollable: true,
              content: SellerContactInfoDialog(
                title: heading,
                subOrder: subOrder,
              ),
            ));
  }

//endregion

  //region Open dialog
  static Future appDialogBox(
      {required BuildContext context,
      required Widget widget,
      bool barrierDismissible = true}) {
    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      useSafeArea: true,
      builder: (_) => SizedBox(
        // height: 300,
        child: AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),

          // scrollable: true,
          content: widget,
          insetPadding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.05),
        ),
      ),
    );
  }

//endregion

  //region App download
  static Future appDownloadDialog() async {
    return appDialogBox(
        context: AppConstants.userStoreCommonBottomNavigationContext,
        widget: const DownloadApp());
  }

  //endregion

  //region Check app

//region Open Dial Pad
  static openDialPad({required String phoneNumber}) async {
    Uri url = Uri(scheme: "tel", path: phoneNumber);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      //print("Can't open dial pad.");
    }
  }

//endregion

  //region Launch social media url
  static void launchSocialMediaUrl({required String url}) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url),
          mode: LaunchMode.externalNonBrowserApplication);
    } else {}
  }

//endregion

//region Open Email
  static openEmail({required String emailId}) async {
    Uri url = Uri(scheme: "mailto", path: emailId);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      //print("Can't open dial pad.");
    }
  }

//endregion

//region Tagged user and url clickable text
  static TextSpan taggedUsedClickableText(
      {required text,
      required BuildContext context,
      required onTapTaggedUser}) {
    return TextSpan(
      style: DefaultTextStyle.of(context).style,
      children: <TextSpan>[
        TextSpan(
            text: '',
            // children: widget.selectedStore.storeDesc!.split(" ").map((value) {
            children: text.split(" ").map((value) {
              ///Tag user name
              if (value.startsWith('@') && value.length > 1) {
                return TextSpan(
                  text: ' ' + value,
                  style: TextStyle(
                      color: AppColors.brandBlack,
                      fontFamily: AppConstants.rRegular,
                      fontSize: 14,
                      fontWeight: FontWeight.w400),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      onTapTaggedUser(value);
                      //print(value);
                    },
                );
              }

              ///Tag url
              if (Uri.parse(value).isAbsolute) {
                return TextSpan(
                  text: ' ' + value,
                  style: TextStyle(
                      color: AppColors.brandBlack,
                      fontFamily: AppConstants.rRegular,
                      fontSize: 14,
                      fontWeight: FontWeight.w400),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      //print(value);
                      //buyerViewStoreBloc.goToProfile();
                    },
                );
              }
              return TextSpan(
                text: ' ' + value,
                style: TextStyle(
                    color: AppColors.appBlack,
                    fontFamily: AppConstants.rRegular,
                    fontSize: 14,
                    fontWeight: FontWeight.w400),
              );
            }).toList()),
      ],
    );
  }

//endregion

  //region Open local filed
  static openLocalFile(
      {required String path, required BuildContext context}) async {
    final result = await OpenFile.open(path);
    if (result.message != "done") {
      toastMessage(result.message, context);
      return;
    }
    return;
  }

//endregion

//region Image crop
  static Future<File?> imageCrop(
      {required File file,
      CropStyle cropStyle = CropStyle.circle,
      CropAspectRatio? customAspectRatio}) async {
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: file.path,
      aspectRatioPresets: [
        CropAspectRatioPreset.original,
      ],
      cropStyle: cropStyle,
      aspectRatio:
          customAspectRatio ?? const CropAspectRatio(ratioX: 5, ratioY: 5),
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Cropper',
          toolbarColor: AppColors.appWhite,
          toolbarWidgetColor: AppColors.appBlack,
          initAspectRatio: CropAspectRatioPreset.original,
          cropFrameColor: Colors.transparent,
          lockAspectRatio: true,
          showCropGrid: false,
          hideBottomControls: true,
        ),

        IOSUiSettings(
          showCancelConfirmationDialog: true,
          title: 'Crop',
          // aspectRatioLockEnabled: true
          //  rotateClockwiseButtonHidden: true
          resetButtonHidden: true,
          rotateButtonsHidden: true,
          hidesNavigationBar: true,
          aspectRatioLockEnabled: true,
          rotateClockwiseButtonHidden: true,
        ),
        // WebUiSettings(
        //   context: context,
        // ),
      ],
    );

    if (croppedFile == null) {
      return null;
    }
    return File(croppedFile.path);
  }

//endregion

//region Find also followed by
  static void alsoFollowedBy({
    required List<Followers> loggedInUserFollowingList,
    required List<Followers> userFollowed,
  }) {}

//endregion

//region Url to website name
  static String urlToWebsiteName({required String url}) {
    Uri? uri = Uri.tryParse(url);
    if (uri != null) {
      List<String> parts = uri.host.split('.');
      if (parts.length >= 2) {
        return parts[0];
      } else {
        return uri.host;
      }
    } else {
      return url;
    }
  }

//endregion

  //region Sub order status to string
  static String subOrderStatusToString({required String subOrderStatus}) {
    //print(subOrderStatus);
    switch (subOrderStatus) {
      ///Waiting
      case AppConstants.waitingForConfirmation:
        return "Waiting for confirmation";
        break;

      ///Payment success
      case AppConstants.subOrderPaymentSuccessStatus:
        return "Payment success";
        break;

      ///Return requested
      case AppConstants.returnRequestedStatus:
        return "Return requested";
        break;

      ///Order confirmed
      case AppConstants.subOrderConfirmedStatus:
        return "Confirmed, not yet shipped";

      ///Delivery in progress
      case AppConstants.deliveryInProgressStatus:
        return "Delivery in progress";
        break;

      ///Return failed
      case AppConstants.returnFailed:
        return "Return failed";
        break;

      ///Return in progress
      case AppConstants.returnInProgress:
        return "Return in progress";
        break;

      ///Return confirmed
      case AppConstants.returnRequestConfirmed:
        return "Return confirmed";
        break;

      ///Order delivered
      case AppConstants.orderDeliveredStatus:
        return "Order delivered";
        break;

      ///Order cancelled by seller
      case AppConstants.orderCanceledBySellerStatus:
        return "Order cancelled by seller";
        break;

      ///Order cancelled by buyer
      case AppConstants.orderCanceledByBuyerStatus:
        return "Order cancelled by buyer";
        break;

      ///Order auto cancelled
      case AppConstants.orderAutoCancelled:
        return "Order auto cancelled";
        break;

      ///Order returned to seller
      case AppConstants.returnedToSeller:
        return "Order returned to seller";
        break;

      ///Payment pending
      case AppConstants.paymentPendingStatus:
        return "Payment pending";
        break;

      ///Order escalated
      case AppConstants.escalatedStatus:
        return "Order escalated";

      // default:
      //   "";
    }
    return subOrderStatus;
  }

  //endregion

  ///Seller  and buyer Order
//region Seller order
//region Return Selected suborder number
  static List<String> sellerSelectedSubOrderNumberList(
      List<SubOrder> subOrderList) {
    List<String> subOrderNumberList = [];
    //Take out suborder number
    for (var subOrder in subOrderList) {
      if (subOrder.isSelected) {
        subOrderNumberList.add(subOrder.suborderNumber!);
      }
    }
    return subOrderNumberList;
  }

//endregion

//region Is all are selected or not
  static void subOrderSelectUnSelectAll(
      {required List<SubOrder> subOrderList, required bool isSelectAll}) {
    for (var data in subOrderList) {
      //if is select all true the mark them all true else mark false
      if (isSelectAll) {
        data.isSelected = true;
      } else {
        data.isSelected = false;
      }
    }
    //print("cleared");
  }

//endregion

//region Seller close bottom sheet and get all orders
  static void sellerCloseBottomSheetAndGetAllOrders(
      {required BuildContext context,
      required Order order,
      required SellerAllOrdersBloc sellerAllOrdersBloc}) {
    ///Add selected order number which will be expand later
    SellerAllOrdersBloc.expandOrderNumber = order.orderNumber!;

    ///Get seller all order
    sellerAllOrdersBloc.getSellerSubOrder();

    ///Close bottom sheet
    Navigator.pop(context);
  }

//endregion

//endregion

  ///Calculate total item in a suborder list
  ///item = quantity * item selling price
  //region Calculate items in sub order list
  static String calculateItemsInSuborderList(
      {required List<SubOrder> subOrderList}) {
    int value = 0;

    //Running for loop and add all the quantity
    for (var data in subOrderList) {
      value = value + data.productQuantity!;
    }
    //If value is 1 then return item else items
    if (value == 1) {
      return "$value ${AppStrings.item}";
    }
    return "$value ${AppStrings.items}";
  }

  //endregion

  ///Calculate total suborders in a suborder list
  //region Calculate suborders in sub order list
  static String calculateSubOrdersInSuborderList(
      {required List<SubOrder> subOrderList}) {
    int value = 0;
    //Length of the suborder
    value = subOrderList.length;
    //If value is 1 then return item else items
    if (value == 1) {
      return "$value ${AppStrings.subOrder}";
    }
    return "$value ${AppStrings.subOrders}";
  }

  //endregion

  //region Add rupee symbol
  static String addRupeeSymbol({required String input}) {
    List<String> numbers = input.split('+');
    String result = '';

    for (int i = 0; i < numbers.length; i++) {
      result += '₹${numbers[i]}';
      if (i < numbers.length - 1) {
        result += ' + ';
      }
    }

    return result;
  }

  //endregion

  //region Shopping card price breakup

  static String shoppingCardPriceBreakup({required String input}) {
    RegExp regex = RegExp(r'\[\s*(\d+)\s*\+\s*(\d+)\s*\]');
    String convertedExpression = input.replaceAllMapped(regex, (match) {
      String number1 = match.group(1)!;
      String number2 = match.group(2)!;
      return '[₹$number1+₹$number2]';
    });
    return convertedExpression;
  }

  //endregion

//region App bottom sheet
  static Future appBottomSheet(
      {required Widget screen,
      required BuildContext context,
      String? bottomSheetName}) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(20), topLeft: Radius.circular(20)),
        ),
        builder: (context) {
          return FractionallySizedBox(
            heightFactor: 0.8,
            child: Column(
              children: [
                Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                          color: bottomSheetName != null
                              ? AppColors.textFieldFill1
                              : Colors.transparent,
                          width: 1),
                    ),
                  ),
                  child: bottomSheetName != null
                      ? Padding(
                          padding: const EdgeInsets.symmetric(vertical: 20),
                          child: Text(
                            bottomSheetName,
                            style: AppTextStyle.pageHeading(
                                textColor: AppColors.appBlack),
                          ),
                        )
                      : const SizedBox(),
                ),
                Expanded(child: screen),
              ],
            ),
          );
        }).then((value) {});
  }

//endregion

  //region App minimum bottom sheets
  static Future<bool?> appMinimumBottomSheets({
    required Widget screen,
    required BuildContext context,
    String? bottomSheetName,
    double? heightPercentage,
  }) {
    Completer<bool?> completer = Completer<bool?>();

    final mediaQuery = MediaQuery.of(context);
    final maxHeight = mediaQuery.size.height * (heightPercentage ?? 0.85);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: AppColors.appWhite,
      constraints: BoxConstraints(
        maxHeight: maxHeight,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(20),
          topLeft: Radius.circular(20),
        ),
      ),
      builder: (context) {
        return SingleChildScrollView(
          padding: EdgeInsets.zero,
          child: Column(
            children: [
              ///Bottom sheet title
              Visibility(
                visible: bottomSheetName != null,
                child: Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border(
                      bottom:
                          BorderSide(color: AppColors.textFieldFill1, width: 1),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Text(
                      bottomSheetName ?? "",
                      style: AppTextStyle.pageHeading(
                          textColor: AppColors.appBlack),
                    ),
                  ),
                ),
              ),

              ///Screen
              screen
            ],
          ),
        );
      },
    ).then((value) {
      completer.complete(value ?? false); // Return false if value is null
    });

    return completer.future;
  }

  //endregion

  //region Access bottom sheet
  static Future<void> accessBottomSheet(
      {required Widget screen, required BuildContext context}) {
    // Check if the screen is ShareWithImageScreen
    bool isShareWithImageScreen =
        screen.runtimeType.toString().contains('ShareWithImageScreen');

    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(20), topLeft: Radius.circular(20))),
        // Use a larger height fraction for ShareWithImageScreen
        constraints: isShareWithImageScreen
            ? BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.9)
            : null,
        builder: (context) {
          return SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // ///Two lines
                  // Container(
                  //   margin: const EdgeInsets.symmetric(vertical: 10),
                  //   child: Column(
                  //     children: [
                  //       SizedBox(
                  //         width: 40,
                  //         child: divider(),
                  //       ),
                  //       verticalSizedBox(5),
                  //       SizedBox(
                  //         width: 40,
                  //         child: divider(),
                  //       ),
                  //     ],
                  //   ),
                  // ),

                  ///Screen
                  screen
                ],
              ));
        }).then((value) {
      // if (value == null) return;
      // supportFilterModel = value;
      // applyFilter();
    });
  }

  //endregion

//region Report and suggestion dialog
  static void reportAndSuggestion({required BuildContext context}) {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => const AlertDialog(
        content: IssueSuggestionDialog(),
      ),
    );
  }

//endregion

//region Singular and plural text
  static String singularPluralText(
      {required int item,
      required String singular,
      required String plural,
      bool isValueReturn = true}) {
    //If item is 1
    if (item == 1) {
      return "${isValueReturn ? item : ""} $singular";
    }
    //else item is greater then 1
    else {
      return "${isValueReturn ? item : ""} $plural";
    }
  }

//endregion

  //region Singular and plural only string
  static String singularPluralOnlyString(
      {required int item,
      required String singular,
      required String plural,
      bool isValueReturn = true}) {
    //If item is 1
    if (item == 1) {
      return "$singular";
    }
    //else item is greater then 1
    else {
      return "$plural";
    }
  }

//endregion

//region Make clickable text
  static List<TextSpan> makeClickableText(
      {required String sentence, required BuildContext context}) {
    List<String> words = sentence.split(" ");
    List<TextSpan> textSpans = [];

    for (String word in words) {
      if (word.startsWith("@")) {
        textSpans.add(
          TextSpan(
            text: "$word ",
            style: AppTextStyle.contentText0(textColor: AppColors.brandGreen),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                OnTapTag(context, word.replaceAll("@", ""));
                // var screen =   UserProfileScreen(userReference: AppConstants.appData.userReference!,);
                // var route = MaterialPageRoute(builder: (context) => screen);
                // Navigator.push(context, route).then((value){
                // });
                // // Handle tap action
                // //print('$word clicked');
              },
          ),
        );
      } else {
        textSpans.add(
          TextSpan(
              text: '$word ',
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack)),
        );
      }
    }

    return textSpans;
  }

//endregion

//region Remove extra commas
  static String removeExtraCommas(String input) {
    // Remove leading and trailing whitespaces
    input = input.trim();

    // Remove extra commas
    input = input.replaceAll(RegExp(r',+'), ',');

    // Remove the last comma if it exists
    if (input.endsWith(',')) {
      input = input.substring(0, input.length - 1);
    }

    return input;
  }

//endregion

//region Open maintenance screen if status is 500
  static openMaintenanceScreen() {
    AppConstants.globalNavigator.currentState?.pushReplacement(
      MaterialPageRoute(builder: (context) => const MaintenanceScreen()),
    );

    // //If bottom navigation in not started then return
    // if(!AppConstants.userStoreCommonBottomNavigationContext.mounted){
    //   return;
    // }
    // else{
    //   Navigator.of(AppConstants.userStoreCommonBottomNavigationContext).pushAndRemoveUntil(MaterialPageRoute(builder: (context) =>
    //       MaintenanceScreen()), (Route<dynamic> route) => false);
    //
    // }
  }

//endregion

//region Calculate notification count
  static String calculateNotificationCount({required int number}) {
    if (number >= 100) {
      return '99+';
    } else if (number > 9) {
      return '9+';
    } else {
      return number.toString();
    }
  }

//endregion

//region Url validation check
  static bool urlValidationCheck({required String url}) {
    // final uriPattern = RegExp(r'^(http(s)?:\/\/(www\.)?[a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)?)$');
    final uriPattern = RegExp(
        r'^((http(s)?:\/\/)?(www\.)?[a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b(.*))$');
    return uriPattern.hasMatch(url);
  }

//endregion

//region Add http and https
  static String addHttpAndHttps({required String url}) {
    if (url.isEmpty) {
      return '';
    } else if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    } else {
      return 'https://$url'; // You can change this to 'https://' if you prefer.
    }
  }

//endregion

  //region Text height
  static double textHeight(
      {required BuildContext context,
      required TextStyle textStyle,
      String text = "Hello"}) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: textStyle),
      textDirection: ui.TextDirection.ltr,
      // textScaleFactor: MediaQuery.textScaleFactorOf(context),
      maxLines: 1,
      ellipsis: '',
    )..layout();
    return textPainter.height + 5;
    // return textPainter.;
  }

//endregion

  //region Text width
  static double textWidth(
      {required BuildContext context,
      required TextStyle textStyle,
      String text = "Hello"}) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: textStyle),
      textDirection: ui.TextDirection.ltr,
      textScaleFactor: MediaQuery.textScaleFactorOf(context),
      maxLines: 1,
      ellipsis: '',
    )..layout();
    return textPainter.width + 5;
  }

  //endregion

//region Font size changer
  static double fontSizeChanger({required int size}) {
    //If app fontSize is null then return original font size
    if (AppConstants.appData.fontSize == null) {
      return size.toDouble();
    }
    return AppConstants.appData.fontSize! + size;
  }

//endregion

//region Open profile image
  static openProfileImage({required String imageUrl}) {
    return showDialog(
      context: AppConstants.userStoreCommonBottomNavigationContext,
      builder: (BuildContext context) {
        return ProfileImageView(
          imageUrl: imageUrl,
        );
      },
    );
  }

//endregion

//region Check network or local
  static bool networkOrLocal(String url) {
    if (url.startsWith('http://') ||
        url.startsWith('https://') ||
        url.startsWith('/media/')) {
      // It's a network URL
      return true;
    } else {
      // It's a local file URL
      return false;
    }
  }

//endregion

//region Celebration
  static void showCelebration(
      {required BuildContext context,
      bool isPlay = true,
      String celebrationMessage = ""}) {
    var screen =
        Celebration(isPlay: isPlay, celebrationMessage: celebrationMessage);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }

//endregion

//region Message on whats app
  static void messageOnWhatsApp(
      {required String phoneNumber, String message = "Hi"}) async {
    // Construct the WhatsApp URL with the phone number and message
    String url = 'https://wa.me/$phoneNumber';
    if (message.isNotEmpty) {
      url += '?text=${Uri.encodeQueryComponent(message)}';
    }

    // Check if the WhatsApp app is installed on the device
    if (await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication)) {
      // Open WhatsApp with the specified URL
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Could not launch WhatsApp.';
    }
  }

//endregion

//region Is image file
  static bool isImageFile({required String fileName}) {
    // List of common image file extensions
    final imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];

    // Get the file extension from the file name
    final fileExtension = fileName.split('.').last.toLowerCase();

    // Check if the file extension is in the list of image extensions
    return imageExtensions.contains(fileExtension);
  }

//endregion

//region Is document file
  static bool isDocumentFile({required String fileName}) {
    // List of common document file extensions
    final documentExtensions = [
      'pdf',
      'doc',
      'docx',
      'txt',
      'rtf',
      'ppt',
      'pptx',
      'xls',
      'xlsx'
    ];

    // Get the file extension from the file name
    final fileExtension = fileName.split('.').last.toLowerCase();

    // Check if the file extension is in the list of document extensions
    return documentExtensions.contains(fileExtension);
  }

//endregion

//region Is video file
  static bool isVideoFile({required String fileName}) {
    // List of common video file extensions
    final videoExtensions = [
      'mp4',
      'avi',
      'mkv',
      'mov',
      'wmv',
      'flv',
      'webm',
      'm4v',
      '3gp'
    ];

    // Get the file extension from the file name
    final fileExtension = fileName.split('.').last.toLowerCase();

    // Check if the file extension is in the list of video extensions
    return videoExtensions.contains(fileExtension);
  }

//endregion

//region If file name is more then 25 character then trim
  static String trimFileName({required String fileName, int trimValue = 20}) {
    if (fileName.length > trimValue) {
      int startIndex = fileName.length - trimValue;
      return fileName.substring(startIndex);
    }
    return fileName;
  }

//endregion

//region Widget height
  static double widgetHeight(
      {required BuildContext context, required Widget widget}) {
    double height = 0.0;
    // Initialize the height
    LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        // Get the height from the constraints
        height = constraints.minHeight;
        return widget;
      },
    );
    return height;
  }

//endregion

//region Label status to sentence
  static dynamic labelStatusToSentence({required String? input}) {
    if (input == null) {
      return null;
    }

    List<String> words = input.split('_');
    for (int i = 0; i < words.length; i++) {
      words[i] =
          words[i][0].toUpperCase() + words[i].substring(1).toLowerCase();
    }
    return words.join(' ');
  }

//endregion

//region Get widget height
  static Future<double> getWidgetHeight({required GlobalKey key}) async {
    Completer<double> completer = Completer<double>();

    // Ensure the widget is laid out
    await Future.delayed(Duration.zero);

    try {
      final RenderBox renderBox =
          key.currentContext!.findRenderObject() as RenderBox;
      final double widgetHeight = renderBox.size.height;
      completer.complete(widgetHeight);
    } catch (e) {
      completer.completeError("Error calculating widget height: $e");
    }

    return completer.future;
  }

//endregion

//region Get text line count
  static int getTextLineCount(
      {required GlobalKey key,
      required String text,
      required TextStyle textStyle}) {
    var size = key.currentContext!.size;
    final textSpan = TextSpan(text: text, style: textStyle);
    final textPainter = TextPainter(
      text: textSpan,
      maxLines: 999, // A high number to avoid cutting off the text.
      textDirection: ui.TextDirection.ltr,
    );
    // print(size!.width);

    // Layout the text to calculate its size.
    textPainter.layout(maxWidth: size!.width);

    // Calculate the number of lines.
    final numberOfLines =
        (textPainter.height / textPainter.preferredLineHeight).ceil();

    return numberOfLines;
  }

//endregion

  //region Image compress
  static Future<File> compressImage({required File originalFile}) async {
    try {
      // // Get a reference to the application's temporary directory.
      // final tempDir = await getTemporaryDirectory();

      // // Generate a unique file name for the compressed image.
      // final uniqueFileName = DateTime.now().millisecondsSinceEpoch.toString();

      // // Create a new file in the temporary directory with the unique file name.
      // final compressedFile = File('${tempDir.path}/$uniqueFileName.jpg');

      // // Compress the original image and write it to the compressed file.
      // await FlutterImageCompress.compressAndGetFile(
      //   originalFile.path,
      //   compressedFile.path,
      //   quality: 100, // Adjust the quality as needed (0-100).
      // );

      return originalFile;
    } catch (e) {
      // Handle any errors that may occur during compression.
      //print("Error compressing image: $e");
      return originalFile;
    }
  }
//endregion

//region Hash generate
//   String generateHash(String data, String privateKey) {
//     // Combine the data and private key
//     String combinedString = "$data$privateKey";
//
//     // Convert the combined string to bytes
//     List<int> combinedBytes = utf8.encode(combinedString);
//
//     // Create a SHA-256 hasher
//     var sha256 = sha256.convert(combinedBytes);
//     var hmacSha256 = Hmac(sha256, key);
//
//     // Convert the hash to a hexadecimal string
//     String hash = sha256.toString();
//
//     return hash;
//   }
//endregion
}
