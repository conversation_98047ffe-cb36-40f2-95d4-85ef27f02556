import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/notification/all_store_notification/all_store_notification_bloc.dart';
import 'package:swadesic/features/notification/notification_common_widget/notification_common_widget.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region All store notification
class AllStoreNotification extends StatefulWidget {
  final Function(int) unSeenCount;

  const AllStoreNotification({super.key, required this.unSeenCount});

  @override
  State<AllStoreNotification> createState() => _AllStoreNotificationState();
}
//endregion

class _AllStoreNotificationState extends State<AllStoreNotification>
    with AutomaticKeepAliveClientMixin<AllStoreNotification> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //region Bloc
  late AllStoreNotificationBloc allStoreNotificationBloc;
  //endregion

  @override
  void initState() {
    allStoreNotificationBloc =
        AllStoreNotificationBloc(context, widget.unSeenCount);
    allStoreNotificationBloc.init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<AllStoreNotificationState>(
        stream: allStoreNotificationBloc.allStoreNotificationCtrl.stream,
        initialData: AllStoreNotificationState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == AllStoreNotificationState.Success) {
            return RefreshIndicator(
              color: AppColors.brandBlack,
              onRefresh: () async {
                await allStoreNotificationBloc.getAllStoreNotification();
              },
              child: ListView.separated(
                separatorBuilder: (context, index) =>
                    const SizedBox(height: 20),
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                physics: const AlwaysScrollableScrollPhysics(
                    parent: BouncingScrollPhysics()),
                itemCount: allStoreNotificationBloc
                    .allStoreNotificationResponse.notifications!.length,
                itemBuilder: (context, index) {
                  return NotificationCommonWidget.notificationCard(
                    onTap: () async {
                      //If web view
                      // if (kIsWeb) {
                      //   await CommonMethods.appDownloadDialog();
                      // } else {
                      //   //else
                      //   allStoreNotificationBloc.updateNotification(
                      //       notificationDetail: allStoreNotificationBloc
                      //           .allStoreNotificationResponse
                      //           .notifications![index]);
                      // }
                      allStoreNotificationBloc.updateNotification(
                          notificationDetail: allStoreNotificationBloc
                              .allStoreNotificationResponse
                              .notifications![index]);
                    },
                    notificationDetail: allStoreNotificationBloc
                        .allStoreNotificationResponse.notifications![index],
                    context: context,
                  );
                },
              ),
            );
          }
          if (snapshot.data == AllStoreNotificationState.Empty) {
            return RefreshIndicator(
              color: AppColors.brandBlack,
              onRefresh: () async {
                await allStoreNotificationBloc.init();
              },
              child: ListView(
                physics: const AlwaysScrollableScrollPhysics(
                    parent: BouncingScrollPhysics()),
                children: [
                  Container(
                    alignment: Alignment.center,
                    height: MediaQuery.of(context).size.height - 200,
                    child: NoResult(message: AppStrings.noNotificationYet),
                  ),
                ],
              ),
            );
          }
          if (snapshot.data == AllStoreNotificationState.Failed) {
            return AppCommonWidgets.errorWidget(onTap: () {
              allStoreNotificationBloc.getAllStoreNotification();
            });
          }
          if (snapshot.data == AllStoreNotificationState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          return const SizedBox();
        });
  }
  //endregion

  //region Filter store
  Widget filterStore() {
    return Row(
      children: [
        Flexible(
          child: Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children: BuyerHomeBloc.storeListResponse.storeList!.map((text) {
              return GestureDetector(
                onTap: () {
                  // Handle row tap
                  //print('Tapped on $text');
                },
                child: Container(
                  height: 50,
                  width: 100,
                  color: Colors.blueGrey,
                  child: Center(
                    child: Text(
                      text.storehandle!,
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
  //endregion
//
//   //region Notification List
//   Widget notificationList() {
//     return SizedBox(
//       height: MediaQuery.of(context).size.height,
//       child: RefreshIndicator(
//         onRefresh: () async{
//           await allStoreNotificationBloc.getAllStoreNotification();
//         },
//         color: AppColors.brandGreen,
//         child: ListView(
//           padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
//           shrinkWrap: true,
//           children: [
//             // filterStore(),
//             today(),
//             yesterday(),
//             thisWeek(),
//             pastWeek(),
//           ],
//         ),
//       ),
//     );
//   }
//   //endregion
//
//
//   //region Today
//   Widget today() {
//
//     if (allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.today!.isNotEmpty) {
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             "Today",
//             style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack1),
//           ),
//           ListView.builder(
//             padding: const EdgeInsets.only(top: 20),
//             physics: const NeverScrollableScrollPhysics(),
//             shrinkWrap: true,
//             itemCount: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.today!.length,
//             itemBuilder: (context, index) {
//               return NotificationCommonWidget.notificationCard(
//                 onTap: () {
//                   allStoreNotificationBloc.updateNotification(notificationDetail: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.today![index]);
//                 },
//                 notificationDetail: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.today![index],
//                 context: context,
//               );
//             },
//           ),
//         ],
//       );
//     } else {
//       return const SizedBox();
//     }
//   }
//
//   //endregion
//
//   //region Yesterday
//   Widget yesterday() {
//     if (allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.yesterday!.isNotEmpty) {
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.yesterday!.isEmpty
//               ? const SizedBox()
//               : Text(
//             "Yesterday",
//             style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack1),
//           ),
//           ListView.builder(
//             padding: const EdgeInsets.only(top: 20),
//             physics: const NeverScrollableScrollPhysics(),
//             shrinkWrap: true,
//             itemCount: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.yesterday!.length,
//             itemBuilder: (context, index) {
//               return NotificationCommonWidget.notificationCard(
//                 onTap: () {
//                   allStoreNotificationBloc.updateNotification(notificationDetail: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.yesterday![index]);
//                 },
//                 notificationDetail: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.yesterday![index],
//                 context: context,
//               );
//             },
//           ),
//         ],
//       );
//     } else {
//       return const SizedBox();
//     }
//   }
//
//   //endregion
//
//   //region This Week
//   Widget thisWeek() {
//     if (allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.thisWeek!.isNotEmpty) {
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.thisWeek!.isEmpty
//               ? const SizedBox()
//               : Text(
//             "Last 7 days",
//             style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack1),
//           ),
//           ListView.builder(
//             padding: const EdgeInsets.only(top: 20),
//             physics: const NeverScrollableScrollPhysics(),
//             shrinkWrap: true,
//             itemCount: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.thisWeek!.length,
//             itemBuilder: (context, index) {
//
//               return NotificationCommonWidget.notificationCard(
//                 onTap: () {
//                   allStoreNotificationBloc.updateNotification(notificationDetail: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.thisWeek![index]);
//                 },
//                 notificationDetail: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.thisWeek![index],
//                 context: context,
//               );
//             },
//           ),
//         ],
//       );
//     } else {
//       return const SizedBox();
//     }
//   }
//
//   //endregion
//
//   //region Past week
//   Widget pastWeek() {
//     if (allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.pastWeek!.isNotEmpty) {
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.pastWeek!.isEmpty
//               ? const SizedBox()
//               : Padding(
//             padding: const EdgeInsets.all(10),
//             child: Text(
//               "Past",
//               style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack1),
//             ),
//           ),
//           ListView.builder(
//             padding: const EdgeInsets.only(top: 20),
//             physics: const NeverScrollableScrollPhysics(),
//             shrinkWrap: true,
//             itemCount: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.pastWeek!.length,
//             itemBuilder: (context, index) {
//
//               return NotificationCommonWidget.notificationCard(
//                 onTap: () {
//                   allStoreNotificationBloc.updateNotification(notificationDetail: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.pastWeek![index]);
//                 },
//                 notificationDetail: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.pastWeek![index],
//                 context: context,
//               );
//               // return notificationCard(notificationDetail: allStoreNotificationBloc.allStoreNotificationDataModel.allStoreNotificationResponse!.pastWeek![index]);
//             },
//           ),
//         ],
//       );
//     } else {
//       return const SizedBox();
//     }
//   }
//
// //endregion
}
