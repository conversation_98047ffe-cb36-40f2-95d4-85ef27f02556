import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/navigation/config/navigation_actions.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/supported_stores/supported_stores_screen.dart';
import 'package:swadesic/features/buyers/user_reward_and_invitees/user_reward_and_invitees_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/post/add_post/add_post_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/seller/add_product/add_product_screen.dart';
import 'package:swadesic/features/seller/deactivate_and_delete_store/deactivate_and_delete_store_screen.dart';
import 'package:swadesic/features/seller/my_plan/my_plan_screen.dart';
import 'package:swadesic/features/seller/seller_accounts/seller_accounts_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/seller_account_balance_and_rewards.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_store_warranty_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivey_setting_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_profile/seller_store_profile_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_screen.dart';
import 'package:swadesic/features/user_profile/edit_user_profile/edit_user_profile_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/user_profile/user_settings/app_and_security/app_and_security_screen.dart';
import 'package:swadesic/features/user_profile/user_settings/app_and_security/app_font_change/app_font_change.dart';
import 'package:swadesic/features/user_profile/user_settings/app_and_security/app_font_change/app_font_change_bloc.dart';
import 'package:swadesic/features/user_profile/user_settings/personal_information/personal_information_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class StoreUserProductNavigation {
  //region Navigate to store product store
  void navigateToStoreProductAndStore({required String references}) {
    String referenceStartWith = references.replaceAll(RegExp(r'\d+'), '');

    //Screen
    late Widget screen;

    //Post
    if (references.startsWith("PO") || references.startsWith("CO")) {
      screen = SinglePostViewScreen(postReference: references);
    }
    //Store
    else if (references.startsWith("S")) {
      screen = BuyerViewStoreScreen(storeReference: references);
    }
    //User
    else if (references.startsWith("U")) {
      screen = UserProfileScreen(userReference: references);
    }
    //Product
    else if (references.startsWith("P")) {
      screen = BuyerViewSingleProductScreen(productReference: references);
    }
    //Order
    else if (references.startsWith("O")) {
      if (!AppConstants.appData.isStoreView!) {
        screen = BuyerSubOrderScreen(orderNumber: references);
      } else {
        screen = SellerSubOrderScreen(
            orderNumber: references, storeId: AppConstants.appData.storeId!);
      }
    }
    // Push to the screen using tab navigation
    final context = AppConstants.currentSelectedTabContext;
    if (context.mounted) {
      // Get the current tab index from the correct persistent tab controller based on view
      final tabController = AppConstants.appData.isStoreView!
          ? AppConstants.storePersistentTabController
          : AppConstants.userPersistentTabController;
      NavigationActions.navigateInTab(tabController.index, screen);
    } else {
      // Fallback to userStoreCommonBottomNavigationContext if current context is not mounted
      final fallbackContext =
          AppConstants.userStoreCommonBottomNavigationContext;
      if (fallbackContext.mounted) {
        // Get the current tab index from the correct persistent tab controller based on view
        final tabController = AppConstants.appData.isStoreView!
            ? AppConstants.storePersistentTabController
            : AppConstants.userPersistentTabController;
        NavigationActions.navigateInTab(tabController.index, screen);
      } else {
        // Last resort: use global navigator
        final globalContext = AppConstants.globalNavigator.currentContext;
        if (globalContext != null && globalContext.mounted) {
          Navigator.push(
            globalContext,
            MaterialPageRoute(builder: (context) => screen),
          );
        }
      }
    }
  }

  //endregion

//region Go to screen
  void goToScreen({required String actionPage}) {
    var screen;

    ///User view
    if (AppConstants.appData.isUserView!) {
      //MY_INVITEES
      if (actionPage == AppScreensEnum.MY_INVITEES.name) {
        screen = const UserRewardAndInviteesScreen(selectedTab: 1);
      }
      //REWARDS
      else if (actionPage == AppScreensEnum.REWARDS.name) {
        screen = const UserRewardAndInviteesScreen(selectedTab: 0);
      }
      //EDIT_USER_PROFILE
      else if (actionPage == AppScreensEnum.EDIT_USER_PROFILE.name) {
        late LoggedInUserInfoDataModel loggedInUserInfoDataModel =
            Provider.of<LoggedInUserInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        screen = EditUserProfileScreen(
            userDetail: loggedInUserInfoDataModel.userDetail!);
      }
      //USER_PERSONAL_INFO
      else if (actionPage == AppScreensEnum.USER_PERSONAL_INFO.name) {
        screen = const PersonalInformationScreen();
      }
      //FIND_ON_SWADESIC
      else if (actionPage == AppScreensEnum.FIND_ON_SWADESIC.name) {
        screen = FindYourCustomersScreen(
          visibleNext: false,
          title: AppStrings.findYourFriendsOnSwadesic,
        );
      }
      //SUPPORTED_STORES
      else if (actionPage == AppScreensEnum.SUPPORTED_STORES.name) {
        screen = const SupportedStoresScreen();
      }
    }

    ///Seller view
    else if (AppConstants.appData.isStoreView!) {
      //MY_INVITEES
      if (actionPage == AppScreensEnum.MY_INVITEES.name) {
        screen = SellerAccountBalanceAndRewards(
          selectedTab: 2,
          storeReference: AppConstants.appData.storeReference!,
        );
      }
      //REWARDS
      else if (actionPage == AppScreensEnum.REWARDS.name) {
        screen = SellerAccountBalanceAndRewards(
            selectedTab: 1,
            storeReference: AppConstants.appData.storeReference!);
      }
      //FIND_ON_SWADESIC
      else if (actionPage == AppScreensEnum.FIND_ON_SWADESIC.name) {
        screen = FindYourCustomersScreen(
          visibleNext: false,
          title: AppStrings.findYourCustomersOnSwadesic,
        );
      }
      //EDIT_STORE_PROFILE
      else if (actionPage == AppScreensEnum.EDIT_STORE_PROFILE.name) {
        screen = SellerStoreProfileScreen(
          storeReference: AppConstants.appData.storeReference!,
        );
      }
      //MY_PLAN
      else if (actionPage == AppScreensEnum.MY_PLAN.name) {
        screen = const MyPlanScreen();
      }
      //TRUST_CENTER
      else if (actionPage == AppScreensEnum.TRUST_CENTER.name) {
        screen = SellerTrustCenterScreen(
          storeRef: AppConstants.appData.storeReference!,
        );
      }
      //DELIVERY_SETTINGS
      else if (actionPage == AppScreensEnum.DELIVERY_SETTINGS.name) {
        screen = SellerStoreDeliverySettingScreen(
          storeRef: AppConstants.appData.storeReference!,
          isFromAddProduct: false,
          isFromEditProduct: false,
          isFromStore: true,
        );
      }
      //RETURN_SETTINGS
      else if (actionPage == AppScreensEnum.RETURN_SETTINGS.name) {
        screen = SellerReturnStoreWarrantyScreen(
          storeRef: AppConstants.appData.storeReference!,
          isFromAddProduct: false,
          fromEditProductScreen: false,
          fromProductScreen: false,
          fromStoreScreen: true,
        );
      }
      //DELETE_AND_DEACTIVATE_STORE
      else if (actionPage == AppScreensEnum.DELETE_AND_DEACTIVATE_STORE.name) {
        screen = DeactivateAndDeleteStoreScreen(
            storeReference: AppConstants.appData.storeReference!);
      }
      //ADD_PRODUCT
      else if (actionPage == AppScreensEnum.ADD_PRODUCT.name) {
        screen = AddProductScreen(
          storeReference: AppConstants.appData.storeReference!,
          storeId: AppConstants.appData.storeId!,
        );
      }
      //ACCOUNT_BALANCE
      else if (actionPage == AppScreensEnum.ACCOUNT_BALANCE.name) {
        screen = SellerAccountBalanceAndRewards(
          selectedTab: 0,
          storeReference: AppConstants.appData.storeReference!,
        );
      }
    }

    ///Common
    else {
      //CREATE_STORE
      if (actionPage == AppScreensEnum.CREATE_STORE.name) {
        screen = SellerAccountsScreen(
            userReference: AppConstants.appData.userReference!);
      }
      //ADD_POST
      else if (actionPage == AppScreensEnum.ADD_POST.name) {
        screen = const AddPostScreen();
      }
      //APP_AND_SECURITY
      else if (actionPage == AppScreensEnum.APP_AND_SECURITY.name) {
        screen = const AppAndSecurityScreen();
      }
      //APP_FONT
      else if (actionPage == AppScreensEnum.APP_FONT.name) {
        screen = const AppFontChangeScreen();
      }
      //INVITE_USER_AND_STORE
      else if (actionPage == AppScreensEnum.INVITE_USER_AND_STORE.name) {
        screen = const CommonReferralPage();
      }
    }

    final context = AppConstants.currentSelectedTabContext;
    if (context.mounted) {
      // Get the current tab index from the correct persistent tab controller based on view
      final tabController = AppConstants.appData.isStoreView!
          ? AppConstants.storePersistentTabController
          : AppConstants.userPersistentTabController;
      NavigationActions.navigateInTab(tabController.index, screen);
    } else {
      // Fallback to userStoreCommonBottomNavigationContext if current context is not mounted
      final fallbackContext =
          AppConstants.userStoreCommonBottomNavigationContext;
      if (fallbackContext.mounted) {
        // Get the current tab index from the correct persistent tab controller based on view
        final tabController = AppConstants.appData.isStoreView!
            ? AppConstants.storePersistentTabController
            : AppConstants.userPersistentTabController;
        NavigationActions.navigateInTab(tabController.index, screen);
      } else {
        // Last resort: use global navigator
        final globalContext = AppConstants.globalNavigator.currentContext;
        if (globalContext != null && globalContext.mounted) {
          Navigator.push(
            globalContext,
            MaterialPageRoute(builder: (context) => screen),
          );
        }
      }
    }
  }

//endregion
}
